"""Tests for code analysis functionality."""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch

from codemind.analysis.parser import <PERSON><PERSON><PERSON><PERSON>, LanguageDetector, SupportedLanguage, ParsedFile
from codemind.analysis.patterns import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CodePattern, PatternType


class TestLanguageDetector:
    """Test language detection."""
    
    def setup_method(self):
        """Set up test environment."""
        self.detector = LanguageDetector()
    
    def test_detect_python_by_extension(self):
        """Test Python detection by file extension."""
        path = Path("test.py")
        language = self.detector.detect_from_path(path)
        assert language == SupportedLanguage.PYTHON
    
    def test_detect_javascript_by_extension(self):
        """Test JavaScript detection by file extension."""
        path = Path("test.js")
        language = self.detector.detect_from_path(path)
        assert language == SupportedLanguage.JAVASCRIPT
    
    def test_detect_python_by_content(self):
        """Test Python detection by content."""
        content = "def hello():\n    print('Hello, world!')"
        language = self.detector.detect_from_content(content)
        assert language == SupportedLanguage.PYTHON
    
    def test_detect_javascript_by_content(self):
        """Test JavaScript detection by content."""
        content = "function hello() {\n    console.log('Hello, world!');\n}"
        language = self.detector.detect_from_content(content)
        assert language == SupportedLanguage.JAVASCRIPT
    
    def test_detect_unknown_extension(self):
        """Test unknown file extension."""
        path = Path("test.unknown")
        language = self.detector.detect_from_path(path)
        assert language is None


class TestCodeParser:
    """Test code parsing functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.parser = CodeParser()
    
    def test_parse_python_file(self, tmp_path):
        """Test parsing a Python file."""
        # Create test file
        test_file = tmp_path / "test.py"
        test_file.write_text("def hello():\n    return 'Hello, world!'")
        
        # Parse file
        parsed = self.parser.parse_file(test_file)
        
        assert parsed is not None
        assert parsed.language == SupportedLanguage.PYTHON
        assert "def hello" in parsed.content
        assert parsed.line_count == 2
    
    def test_parse_nonexistent_file(self):
        """Test parsing a non-existent file."""
        parsed = self.parser.parse_file(Path("nonexistent.py"))
        assert parsed is None
    
    def test_parse_directory(self, tmp_path):
        """Test parsing a directory."""
        # Create test files
        (tmp_path / "test1.py").write_text("print('test1')")
        (tmp_path / "test2.js").write_text("console.log('test2');")
        (tmp_path / "README.md").write_text("# Test")
        
        # Parse directory
        parsed_files = self.parser.parse_directory(tmp_path, recursive=False)
        
        # Should find Python and JavaScript files
        languages = {f.language for f in parsed_files}
        assert SupportedLanguage.PYTHON in languages
        assert SupportedLanguage.JAVASCRIPT in languages
        assert len(parsed_files) >= 2
    
    def test_get_supported_languages(self):
        """Test getting supported languages."""
        languages = self.parser.get_supported_languages()
        assert SupportedLanguage.PYTHON in languages
        assert SupportedLanguage.JAVASCRIPT in languages
        assert len(languages) > 0


class TestPatternExtractor:
    """Test pattern extraction."""
    
    def setup_method(self):
        """Set up test environment."""
        self.extractor = PatternExtractor()
    
    def test_extract_python_patterns(self):
        """Test extracting patterns from Python code."""
        # Create mock parsed file
        python_code = '''
import os
from typing import List

def hello_world(name: str) -> str:
    """Say hello to someone."""
    return f"Hello, {name}!"

class TestClass:
    def __init__(self, value):
        self.value = value
    
    def __str__(self):
        return str(self.value)
'''
        
        parsed_file = ParsedFile(
            path=Path("test.py"),
            language=SupportedLanguage.PYTHON,
            content=python_code
        )
        
        patterns = self.extractor.extract_from_file(parsed_file)
        
        # Should find various patterns
        pattern_types = {p.type for p in patterns}
        assert PatternType.IMPORT_STYLE in pattern_types
        assert PatternType.FUNCTION_SIGNATURE in pattern_types
        assert PatternType.CLASS_STRUCTURE in pattern_types
    
    def test_extract_from_multiple_files(self):
        """Test extracting patterns from multiple files."""
        # Create multiple mock files
        files = [
            ParsedFile(
                path=Path("file1.py"),
                language=SupportedLanguage.PYTHON,
                content="def func1(): pass"
            ),
            ParsedFile(
                path=Path("file2.py"),
                language=SupportedLanguage.PYTHON,
                content="def func2(): pass"
            )
        ]
        
        patterns = self.extractor.extract_from_files(files)
        
        # Should consolidate similar patterns
        assert len(patterns) > 0
        
        # Check that patterns have correct frequency
        for pattern in patterns:
            assert pattern.frequency >= 1
    
    def test_pattern_summary(self):
        """Test pattern summary generation."""
        patterns = [
            CodePattern(
                type=PatternType.FUNCTION_SIGNATURE,
                language=SupportedLanguage.PYTHON,
                pattern="type_annotations",
                description="Uses type annotations",
                frequency=5
            ),
            CodePattern(
                type=PatternType.IMPORT_STYLE,
                language=SupportedLanguage.PYTHON,
                pattern="from_imports",
                description="Uses from imports",
                frequency=3
            )
        ]
        
        summary = self.extractor.get_pattern_summary(patterns)
        
        assert summary["total_patterns"] == 2
        assert "function_signature" in summary["by_type"]
        assert "import_style" in summary["by_type"]
        assert summary["by_type"]["function_signature"] == 1


@pytest.mark.integration
class TestAnalysisIntegration:
    """Integration tests for analysis components."""
    
    def test_full_analysis_workflow(self, tmp_path):
        """Test complete analysis workflow."""
        # Create test project structure
        (tmp_path / "main.py").write_text('''
import sys
from typing import List

def main(args: List[str]) -> int:
    """Main function."""
    print("Hello, CodeMind!")
    return 0

if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
''')
        
        (tmp_path / "utils.py").write_text('''
from pathlib import Path

class FileHelper:
    def __init__(self, base_path: Path):
        self.base_path = base_path
    
    def read_file(self, filename: str) -> str:
        return (self.base_path / filename).read_text()
''')
        
        # Parse files
        parser = CodeParser()
        parsed_files = parser.parse_directory(tmp_path)
        
        assert len(parsed_files) == 2
        assert all(f.language == SupportedLanguage.PYTHON for f in parsed_files)
        
        # Extract patterns
        extractor = PatternExtractor()
        patterns = extractor.extract_from_files(parsed_files)
        
        assert len(patterns) > 0
        
        # Verify pattern types found
        pattern_types = {p.type for p in patterns}
        expected_types = {
            PatternType.IMPORT_STYLE,
            PatternType.FUNCTION_SIGNATURE,
            PatternType.CLASS_STRUCTURE,
            PatternType.NAMING_CONVENTION
        }
        
        # Should find at least some expected patterns
        assert len(pattern_types.intersection(expected_types)) > 0
