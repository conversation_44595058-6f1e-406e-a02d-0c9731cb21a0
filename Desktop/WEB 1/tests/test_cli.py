"""Tests for CLI functionality."""

import pytest
from pathlib import Path
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch, MagicMock

from codemind.cli.main import app


class TestCLI:
    """Test CLI commands."""
    
    def setup_method(self):
        """Set up test environment."""
        self.runner = CliRunner()
    
    def test_version_command(self):
        """Test version command."""
        result = self.runner.invoke(app, ["version"])
        assert result.exit_code == 0
        assert "CodeMind" in result.stdout
    
    def test_init_command_help(self):
        """Test init command help."""
        result = self.runner.invoke(app, ["init", "--help"])
        assert result.exit_code == 0
        assert "Initialize CodeMind" in result.stdout
    
    def test_status_command_not_initialized(self):
        """Test status command when not initialized."""
        with patch('codemind.cli.main.cli_manager') as mock_manager:
            mock_manager.ensure_initialized.return_value = False
            result = self.runner.invoke(app, ["status"])
            assert result.exit_code == 1
            assert "not initialized" in result.stdout
    
    def test_analyze_command_help(self):
        """Test analyze command help."""
        result = self.runner.invoke(app, ["analyze", "--help"])
        assert result.exit_code == 0
        assert "Analyze codebase" in result.stdout
    
    def test_suggest_command_help(self):
        """Test suggest command help."""
        result = self.runner.invoke(app, ["suggest", "--help"])
        assert result.exit_code == 0
        assert "Get AI-powered code suggestions" in result.stdout
    
    def test_learn_command_help(self):
        """Test learn command help."""
        result = self.runner.invoke(app, ["learn", "--help"])
        assert result.exit_code == 0
        assert "Learn from code changes" in result.stdout


class TestCLIIntegration:
    """Integration tests for CLI."""
    
    def setup_method(self):
        """Set up test environment."""
        self.runner = CliRunner()
    
    @pytest.mark.integration
    def test_init_workflow(self, tmp_path):
        """Test complete initialization workflow."""
        with self.runner.isolated_filesystem(temp_dir=tmp_path):
            # Test initialization
            result = self.runner.invoke(app, ["init", "--no-interactive"])
            assert result.exit_code == 0
            assert "initialized successfully" in result.stdout
            
            # Check that .codemind directory was created
            assert Path(".codemind").exists()
            assert Path(".codemind/config.yaml").exists()
            
            # Test status after initialization
            result = self.runner.invoke(app, ["status"])
            assert result.exit_code == 0
            assert "CodeMind Status" in result.stdout
