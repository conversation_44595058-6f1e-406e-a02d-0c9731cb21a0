#!/usr/bin/env python3
"""
CodeMind Demo Script

This script demonstrates the core functionality of CodeMind,
including code analysis, pattern extraction, and the foundation
for the self-evolving spider web learning system.
"""

import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add src to path for demo
sys.path.insert(0, str(Path(__file__).parent / "src"))

from codemind.core.config import Config
from codemind.core.logging import setup_logging
from codemind.analysis.parser import CodeParser, SupportedLanguage, ParsedFile
from codemind.analysis.patterns import PatternExtractor, PatternType
from codemind.ml.models import model_registry, SupportedModel
from codemind.spider.coordinator import SpiderCoordinator, LearningNode, NodeStatus

console = Console()


def create_sample_codebase(base_path: Path) -> None:
    """Create a sample codebase for demonstration."""
    
    # Python files
    (base_path / "main.py").write_text('''
#!/usr/bin/env python3
"""
Main application entry point.
"""

import sys
import logging
from typing import List, Optional
from pathlib import Path

from utils import FileProcessor, ConfigManager
from models import User, Project


def setup_logging(level: str = "INFO") -> None:
    """Set up application logging."""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )


class Application:
    """Main application class."""
    
    def __init__(self, config_path: Optional[Path] = None):
        self.config = ConfigManager(config_path)
        self.processor = FileProcessor(self.config.get("data_dir"))
        self.users: List[User] = []
        self.projects: List[Project] = []
    
    def run(self) -> int:
        """Run the application."""
        try:
            self.load_data()
            self.process_projects()
            return 0
        except Exception as e:
            logging.error(f"Application failed: {e}")
            return 1
    
    def load_data(self) -> None:
        """Load application data."""
        user_files = self.processor.find_files("*.json", "users")
        for file_path in user_files:
            user = User.from_file(file_path)
            self.users.append(user)
        
        logging.info(f"Loaded {len(self.users)} users")
    
    def process_projects(self) -> None:
        """Process all projects."""
        for user in self.users:
            for project in user.projects:
                self.processor.process_project(project)


def main(args: List[str]) -> int:
    """Main entry point."""
    setup_logging()
    
    config_path = Path(args[0]) if args else None
    app = Application(config_path)
    
    return app.run()


if __name__ == "__main__":
    sys.exit(main(sys.argv[1:]))
''')
    
    (base_path / "utils.py").write_text('''
"""
Utility functions and classes.
"""

import json
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class ConfigManager:
    """Manages application configuration."""
    
    config_path: Optional[Path] = None
    _config: Dict[str, Any] = None
    
    def __post_init__(self):
        self.load_config()
    
    def load_config(self) -> None:
        """Load configuration from file."""
        if self.config_path and self.config_path.exists():
            with open(self.config_path, 'r') as f:
                self._config = json.load(f)
        else:
            self._config = self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "data_dir": "data",
            "log_level": "INFO",
            "max_workers": 4,
            "timeout": 30
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self._config.get(key, default)


class FileProcessor:
    """Processes files and directories."""
    
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
    
    def find_files(self, pattern: str, subdir: str = "") -> List[Path]:
        """Find files matching pattern."""
        search_dir = self.base_dir / subdir if subdir else self.base_dir
        return list(search_dir.glob(pattern))
    
    def process_project(self, project) -> None:
        """Process a project."""
        project_dir = self.base_dir / "projects" / project.name
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy project files
        if project.source_path.exists():
            shutil.copytree(project.source_path, project_dir, dirs_exist_ok=True)
    
    def cleanup_temp_files(self) -> None:
        """Clean up temporary files."""
        temp_dir = self.base_dir / "temp"
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
''')
    
    (base_path / "models.py").write_text('''
"""
Data models for the application.
"""

import json
from pathlib import Path
from typing import List, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class User:
    """Represents a user in the system."""
    
    id: str
    name: str
    email: str
    created_at: datetime
    projects: List["Project"]
    
    @classmethod
    def from_file(cls, file_path: Path) -> "User":
        """Load user from JSON file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        projects = [Project(**p) for p in data.get("projects", [])]
        
        return cls(
            id=data["id"],
            name=data["name"],
            email=data["email"],
            created_at=datetime.fromisoformat(data["created_at"]),
            projects=projects
        )
    
    def save_to_file(self, file_path: Path) -> None:
        """Save user to JSON file."""
        data = {
            "id": self.id,
            "name": self.name,
            "email": self.email,
            "created_at": self.created_at.isoformat(),
            "projects": [p.__dict__ for p in self.projects]
        }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)


@dataclass
class Project:
    """Represents a project."""
    
    name: str
    description: str
    language: str
    source_path: Path
    status: str = "active"
    
    def __post_init__(self):
        if isinstance(self.source_path, str):
            self.source_path = Path(self.source_path)
    
    @property
    def is_active(self) -> bool:
        """Check if project is active."""
        return self.status == "active"
    
    def get_file_count(self) -> int:
        """Get number of files in project."""
        if not self.source_path.exists():
            return 0
        
        extensions = {
            "python": [".py"],
            "javascript": [".js", ".jsx"],
            "typescript": [".ts", ".tsx"],
        }
        
        file_extensions = extensions.get(self.language.lower(), [".py"])
        count = 0
        
        for ext in file_extensions:
            count += len(list(self.source_path.glob(f"**/*{ext}")))
        
        return count
''')
    
    # JavaScript file
    (base_path / "frontend.js").write_text('''
/**
 * Frontend JavaScript for the application.
 */

class UserInterface {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.users = [];
        this.projects = [];
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadData();
    }
    
    setupEventListeners() {
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadData());
        }
        
        const addUserBtn = document.getElementById('add-user-btn');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => this.showAddUserDialog());
        }
    }
    
    async loadData() {
        try {
            const response = await fetch('/api/users');
            this.users = await response.json();
            this.renderUsers();
        } catch (error) {
            console.error('Failed to load users:', error);
            this.showError('Failed to load user data');
        }
    }
    
    renderUsers() {
        const userList = this.container.querySelector('.user-list');
        if (!userList) return;
        
        userList.innerHTML = '';
        
        this.users.forEach(user => {
            const userElement = this.createUserElement(user);
            userList.appendChild(userElement);
        });
    }
    
    createUserElement(user) {
        const div = document.createElement('div');
        div.className = 'user-card';
        div.innerHTML = `
            <h3>${user.name}</h3>
            <p>Email: ${user.email}</p>
            <p>Projects: ${user.projects.length}</p>
            <button onclick="ui.viewUser('${user.id}')">View Details</button>
        `;
        return div;
    }
    
    showAddUserDialog() {
        const dialog = document.getElementById('add-user-dialog');
        if (dialog) {
            dialog.style.display = 'block';
        }
    }
    
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        this.container.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

// Initialize UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.ui = new UserInterface('app-container');
});
''')


async def demo_code_analysis():
    """Demonstrate code analysis capabilities."""
    console.print(Panel.fit(
        "🔍 [bold blue]Code Analysis Demo[/bold blue]\n\n"
        "Analyzing sample codebase for patterns and structure...",
        title="Step 1: Analysis"
    ))
    
    # Create sample codebase
    sample_dir = Path("demo_codebase")
    sample_dir.mkdir(exist_ok=True)
    create_sample_codebase(sample_dir)
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Analyzing codebase...", total=None)
        
        # Initialize parser
        parser = CodeParser()
        
        # Parse files
        progress.update(task, description="Parsing source files...")
        parsed_files = parser.parse_directory(sample_dir, recursive=True)
        
        # Extract patterns
        progress.update(task, description="Extracting patterns...")
        extractor = PatternExtractor()
        patterns = extractor.extract_from_files(parsed_files)
        
        progress.update(task, description="Analysis complete!")
    
    # Display results
    table = Table(title="📊 Analysis Results", show_header=True, header_style="bold blue")
    table.add_column("Metric", style="cyan", no_wrap=True)
    table.add_column("Value", style="green")
    
    table.add_row("Files Analyzed", str(len(parsed_files)))
    table.add_row("Languages Detected", str(len(set(f.language for f in parsed_files))))
    table.add_row("Patterns Found", str(len(patterns)))
    table.add_row("Total Lines", str(sum(f.line_count for f in parsed_files)))
    
    console.print(table)
    
    # Show pattern details
    if patterns:
        pattern_table = Table(title="🎯 Discovered Patterns", show_header=True)
        pattern_table.add_column("Type", style="cyan")
        pattern_table.add_column("Pattern", style="yellow")
        pattern_table.add_column("Language", style="green")
        pattern_table.add_column("Confidence", style="magenta")
        
        for pattern in patterns[:5]:  # Show top 5
            pattern_table.add_row(
                pattern.type.value,
                pattern.pattern,
                pattern.language.value,
                f"{pattern.confidence:.2f}"
            )
        
        console.print(pattern_table)
    
    return parsed_files, patterns


async def demo_model_system():
    """Demonstrate the model system."""
    console.print(Panel.fit(
        "🤖 [bold blue]Model System Demo[/bold blue]\n\n"
        "Exploring available models and capabilities...",
        title="Step 2: Models"
    ))
    
    # Show available models
    models_table = Table(title="📦 Available Models", show_header=True)
    models_table.add_column("Model", style="cyan")
    models_table.add_column("Size", style="yellow")
    models_table.add_column("Memory (GB)", style="green")
    models_table.add_column("Languages", style="magenta")
    models_table.add_column("Local Compatible", style="blue")
    
    for model in list(SupportedModel)[:5]:  # Show first 5
        spec = model_registry.get_model_spec(model)
        if spec:
            models_table.add_row(
                spec.name,
                f"{spec.download_size_gb:.1f}GB",
                f"{spec.memory_requirements_gb:.1f}",
                ", ".join(spec.capabilities.supported_languages[:3]),
                "✅" if spec.is_local_compatible else "❌"
            )
    
    console.print(models_table)
    
    # Show model recommendations
    console.print("\n💡 [bold]Model Recommendations:[/bold]")
    
    for memory_limit in [4, 8, 16]:
        recommended = model_registry.recommend_model(
            language="python",
            max_memory_gb=memory_limit,
            prefer_quality=True
        )
        
        if recommended:
            spec = model_registry.get_model_spec(recommended)
            console.print(f"  • {memory_limit}GB RAM: [cyan]{spec.name}[/cyan]")


async def demo_spider_network():
    """Demonstrate the spider web learning system."""
    console.print(Panel.fit(
        "🕸️  [bold blue]Spider Web Learning Demo[/bold blue]\n\n"
        "Simulating distributed learning network...",
        title="Step 3: Spider Network"
    ))
    
    # Initialize coordinator
    config = Config()
    config.spider.enable_federated_learning = True
    coordinator = SpiderCoordinator(config)
    
    # Create mock nodes
    nodes = []
    for i in range(3):
        node = LearningNode(
            node_id=f"node_{i+1}",
            public_key=f"pubkey_{i+1}",
            last_seen=datetime.now(),
            status=NodeStatus.ONLINE
        )
        nodes.append(node)
        await coordinator.register_node(node)
    
    # Show network status
    status = coordinator.get_network_status()
    
    network_table = Table(title="🌐 Network Status", show_header=True)
    network_table.add_column("Metric", style="cyan")
    network_table.add_column("Value", style="green")
    
    network_table.add_row("Total Nodes", str(status["total_nodes"]))
    network_table.add_row("Active Nodes", str(status["active_nodes"]))
    network_table.add_row("Cached Patterns", str(status["cached_patterns"]))
    network_table.add_row("Federated Learning", "✅ Enabled" if status["federated_learning_enabled"] else "❌ Disabled")
    
    console.print(network_table)
    
    # Show node details
    nodes_table = Table(title="🔗 Connected Nodes", show_header=True)
    nodes_table.add_column("Node ID", style="cyan")
    nodes_table.add_column("Status", style="green")
    nodes_table.add_column("Patterns Shared", style="yellow")
    nodes_table.add_column("Trust Score", style="magenta")
    
    for node in nodes:
        nodes_table.add_row(
            node.node_id,
            node.status.value,
            str(node.patterns_shared),
            f"{node.trust_score:.2f}"
        )
    
    console.print(nodes_table)


async def demo_integration():
    """Demonstrate full system integration."""
    console.print(Panel.fit(
        "🔄 [bold blue]System Integration Demo[/bold blue]\n\n"
        "Showing how all components work together...",
        title="Step 4: Integration"
    ))
    
    # Simulate learning workflow
    workflow_steps = [
        "📁 Scanning local codebase",
        "🔍 Extracting code patterns", 
        "🧠 Analyzing with local model",
        "🔒 Anonymizing sensitive data",
        "🕸️  Sharing with spider network",
        "📥 Receiving network updates",
        "🎯 Updating local knowledge",
        "✨ Ready for improved suggestions"
    ]
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("Processing...", total=len(workflow_steps))
        
        for i, step in enumerate(workflow_steps):
            progress.update(task, description=step, completed=i)
            await asyncio.sleep(0.5)  # Simulate work
        
        progress.update(task, description="Integration complete!", completed=len(workflow_steps))
    
    # Show final status
    console.print("\n🎉 [bold green]Integration Complete![/bold green]")
    console.print("\n📋 [bold]What CodeMind can now do:[/bold]")
    console.print("  • ✅ Analyze your codebase for patterns")
    console.print("  • ✅ Generate code suggestions based on your style")
    console.print("  • ✅ Learn from your coding patterns over time")
    console.print("  • ✅ Optionally share anonymized insights with the network")
    console.print("  • ✅ Receive improvements from the global knowledge base")
    console.print("  • ✅ Maintain complete privacy and data control")


async def main():
    """Run the complete demo."""
    # Setup
    config = Config()
    config.debug = True
    setup_logging(config)
    
    console.print(Panel.fit(
        "🧠 [bold blue]Welcome to CodeMind![/bold blue]\n\n"
        "The Self-Evolving Code Assistant\n"
        "This demo showcases the core capabilities and vision.",
        title="CodeMind Demo"
    ))
    
    try:
        # Run demo sections
        await demo_code_analysis()
        await demo_model_system()
        await demo_spider_network()
        await demo_integration()
        
        # Final message
        console.print(Panel.fit(
            "🚀 [bold green]Demo Complete![/bold green]\n\n"
            "CodeMind is ready to evolve with your codebase.\n\n"
            "[bold]Next Steps:[/bold]\n"
            "• Run 'python scripts/setup.py' to set up development environment\n"
            "• Try 'codemind init' in your project directory\n"
            "• Explore the examples/ directory for more usage patterns\n"
            "• Check out the documentation for advanced features",
            title="Success!"
        ))
        
    except KeyboardInterrupt:
        console.print("\n👋 Demo interrupted. Thanks for trying CodeMind!")
    except Exception as e:
        console.print(f"\n❌ Demo failed: {e}")
        import traceback
        console.print(traceback.format_exc())


if __name__ == "__main__":
    # Import datetime here to avoid issues
    from datetime import datetime
    asyncio.run(main())
