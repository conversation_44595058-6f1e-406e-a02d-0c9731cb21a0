"""
ML inference engine for CodeMind.

This module handles model loading, inference, and code generation
using various transformer models optimized for code tasks.
"""

import os
import gc
import torch
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
import threading
from contextlib import contextmanager

try:
    from transformers import (
        AutoTokenizer, AutoModelForCausalLM, 
        BitsAndBytesConfig, GenerationConfig,
        pipeline, Pipeline
    )
    from accelerate import init_empty_weights, load_checkpoint_and_dispatch
except ImportError:
    AutoTokenizer = None
    AutoModelForCausalLM = None
    BitsAndBytesConfig = None
    GenerationConfig = None
    pipeline = None
    Pipeline = None

from .models import SupportedModel, model_registry, ModelSpec
from ..core.config import Config
from ..core.logging import LoggerMixin
from ..core.exceptions import ModelError


@dataclass
class GenerationRequest:
    """Request for code generation."""
    
    prompt: str
    language: str = "python"
    max_tokens: int = 256
    temperature: float = 0.1
    top_p: float = 0.9
    stop_sequences: Optional[List[str]] = None
    context: Optional[str] = None
    file_path: Optional[str] = None
    
    def __post_init__(self):
        if self.stop_sequences is None:
            # Default stop sequences for code
            self.stop_sequences = ["\n\n\n", "```", "# End", "// End"]


@dataclass 
class GenerationResponse:
    """Response from code generation."""
    
    generated_code: str
    prompt: str
    model_used: str
    generation_time_ms: float
    tokens_generated: int
    confidence_score: float = 0.0
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class ModelManager(LoggerMixin):
    """Manages ML model loading, caching, and lifecycle."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self._loaded_models: Dict[str, Any] = {}
        self._tokenizers: Dict[str, Any] = {}
        self._model_lock = threading.Lock()
        self._current_model: Optional[str] = None
        
        # Check hardware capabilities
        self.device = self._detect_device()
        self.max_memory = self._detect_max_memory()
        
        self.logger.info("ModelManager initialized", 
                        device=self.device, 
                        max_memory_gb=self.max_memory)
    
    def _detect_device(self) -> str:
        """Detect the best available device for inference."""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"  # Apple Silicon
        else:
            return "cpu"
    
    def _detect_max_memory(self) -> float:
        """Detect maximum available memory in GB."""
        if self.device == "cuda":
            try:
                return torch.cuda.get_device_properties(0).total_memory / (1024**3)
            except:
                return 8.0  # Conservative default
        elif self.device == "mps":
            # Apple Silicon - use system memory heuristic
            import psutil
            return psutil.virtual_memory().total / (1024**3) * 0.6  # 60% of system RAM
        else:
            # CPU - use system memory heuristic  
            import psutil
            return psutil.virtual_memory().total / (1024**3) * 0.4  # 40% of system RAM
    
    def get_quantization_config(self, model_spec: ModelSpec) -> Optional[BitsAndBytesConfig]:
        """Get quantization configuration based on model and hardware."""
        if BitsAndBytesConfig is None:
            return None
        
        # Only quantize if model is large and we have limited memory
        if model_spec.memory_requirements_gb > self.max_memory * 0.8:
            if self.device == "cuda":
                # Use 4-bit quantization for CUDA
                return BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                    bnb_4bit_use_double_quant=True,
                    bnb_4bit_quant_type="nf4"
                )
            else:
                # Use 8-bit for other devices
                return BitsAndBytesConfig(
                    load_in_8bit=True,
                    llm_int8_enable_fp32_cpu_offload=True
                )
        
        return None
    
    def load_model(self, model: SupportedModel, force_reload: bool = False) -> bool:
        """Load a model into memory."""
        if AutoTokenizer is None or AutoModelForCausalLM is None:
            raise ModelError("Transformers library not available")
        
        model_id = model.value
        
        with self._model_lock:
            # Check if already loaded
            if model_id in self._loaded_models and not force_reload:
                self._current_model = model_id
                self.logger.info("Model already loaded", model=model_id)
                return True
            
            try:
                model_spec = model_registry.get_model_spec(model)
                if not model_spec:
                    raise ModelError(f"Unknown model: {model}")
                
                # Check memory requirements
                if model_spec.memory_requirements_gb > self.max_memory:
                    self.logger.warning("Model may not fit in available memory",
                                      required_gb=model_spec.memory_requirements_gb,
                                      available_gb=self.max_memory)
                
                self.logger.info("Loading model", model=model_id, device=self.device)
                
                # Load tokenizer
                tokenizer = AutoTokenizer.from_pretrained(
                    model_spec.model_id,
                    cache_dir=str(self.config.models_path),
                    trust_remote_code=True
                )
                
                # Set pad token if not present
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                # Get quantization config
                quantization_config = self.get_quantization_config(model_spec)
                
                # Load model
                model_kwargs = {
                    "cache_dir": str(self.config.models_path),
                    "trust_remote_code": True,
                    "torch_dtype": torch.float16 if self.device != "cpu" else torch.float32,
                }
                
                if quantization_config:
                    model_kwargs["quantization_config"] = quantization_config
                    model_kwargs["device_map"] = "auto"
                else:
                    model_kwargs["device_map"] = self.device
                
                model_instance = AutoModelForCausalLM.from_pretrained(
                    model_spec.model_id,
                    **model_kwargs
                )
                
                # Store loaded components
                self._tokenizers[model_id] = tokenizer
                self._loaded_models[model_id] = model_instance
                self._current_model = model_id
                
                self.logger.info("Model loaded successfully", 
                               model=model_id,
                               quantized=quantization_config is not None)
                
                return True
                
            except Exception as e:
                self.logger.error("Failed to load model", model=model_id, error=str(e))
                raise ModelError(f"Failed to load model {model_id}: {e}")
    
    def unload_model(self, model: Optional[SupportedModel] = None) -> None:
        """Unload a model from memory."""
        with self._model_lock:
            if model is None:
                # Unload current model
                model_id = self._current_model
            else:
                model_id = model.value
            
            if model_id and model_id in self._loaded_models:
                del self._loaded_models[model_id]
                del self._tokenizers[model_id]
                
                if model_id == self._current_model:
                    self._current_model = None
                
                # Force garbage collection
                gc.collect()
                if self.device == "cuda":
                    torch.cuda.empty_cache()
                
                self.logger.info("Model unloaded", model=model_id)
    
    def get_current_model(self) -> Optional[str]:
        """Get the currently loaded model ID."""
        return self._current_model
    
    def is_model_loaded(self, model: SupportedModel) -> bool:
        """Check if a model is currently loaded."""
        return model.value in self._loaded_models
    
    @contextmanager
    def model_context(self, model: SupportedModel):
        """Context manager for temporary model loading."""
        was_loaded = self.is_model_loaded(model)

        if not was_loaded:
            self.load_model(model)

        try:
            yield
        finally:
            if not was_loaded:
                self.unload_model(model)


class CodeGenerator(LoggerMixin):
    """High-level interface for code generation."""

    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.model_manager = ModelManager(config)
        self._default_model = None
        self._initialize_default_model()

    def _initialize_default_model(self) -> None:
        """Initialize the default model based on configuration."""
        try:
            # Try to find a suitable model based on available memory
            recommended = model_registry.recommend_model(
                language="python",
                max_memory_gb=self.model_manager.max_memory,
                prefer_quality=False  # Prefer speed for default
            )

            if recommended:
                self._default_model = recommended
                self.logger.info("Default model selected", model=recommended.value)
            else:
                self.logger.warning("No suitable model found for current hardware")

        except Exception as e:
            self.logger.error("Failed to initialize default model", error=str(e))

    def generate_code(
        self,
        request: GenerationRequest,
        model: Optional[SupportedModel] = None
    ) -> GenerationResponse:
        """Generate code based on the request."""
        import time

        # Use default model if none specified
        if model is None:
            model = self._default_model

        if model is None:
            raise ModelError("No model available for code generation")

        start_time = time.time()

        try:
            # Ensure model is loaded
            if not self.model_manager.is_model_loaded(model):
                self.model_manager.load_model(model)

            # Get model components
            model_id = model.value
            tokenizer = self.model_manager._tokenizers[model_id]
            model_instance = self.model_manager._loaded_models[model_id]

            # Prepare prompt
            formatted_prompt = self._format_prompt(request)

            # Tokenize input
            inputs = tokenizer(
                formatted_prompt,
                return_tensors="pt",
                truncation=True,
                max_length=2048
            )

            # Move to device
            device = self.model_manager.device
            inputs = {k: v.to(device) for k, v in inputs.items()}

            # Generate
            with torch.no_grad():
                outputs = model_instance.generate(
                    **inputs,
                    max_new_tokens=request.max_tokens,
                    temperature=request.temperature,
                    top_p=request.top_p,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )

            # Decode output
            generated_tokens = outputs[0][inputs['input_ids'].shape[1]:]
            generated_text = tokenizer.decode(generated_tokens, skip_special_tokens=True)

            # Post-process
            generated_code = self._post_process_output(generated_text, request)

            # Calculate metrics
            generation_time = (time.time() - start_time) * 1000  # ms
            tokens_generated = len(generated_tokens)

            response = GenerationResponse(
                generated_code=generated_code,
                prompt=request.prompt,
                model_used=model_id,
                generation_time_ms=generation_time,
                tokens_generated=tokens_generated,
                metadata={
                    "temperature": request.temperature,
                    "top_p": request.top_p,
                    "language": request.language,
                }
            )

            self.logger.info("Code generation completed",
                           model=model_id,
                           tokens=tokens_generated,
                           time_ms=generation_time)

            return response

        except Exception as e:
            self.logger.error("Code generation failed", model=model.value, error=str(e))
            raise ModelError(f"Code generation failed: {e}")

    def _format_prompt(self, request: GenerationRequest) -> str:
        """Format the prompt for the model."""
        prompt_parts = []

        # Add context if provided
        if request.context:
            prompt_parts.append(f"# Context:\n{request.context}\n")

        # Add file path context if provided
        if request.file_path:
            prompt_parts.append(f"# File: {request.file_path}\n")

        # Add language-specific formatting
        if request.language == "python":
            prompt_parts.append("# Python code:\n")
        elif request.language == "javascript":
            prompt_parts.append("// JavaScript code:\n")
        elif request.language == "typescript":
            prompt_parts.append("// TypeScript code:\n")

        # Add the main prompt
        prompt_parts.append(request.prompt)

        return "\n".join(prompt_parts)

    def _post_process_output(self, generated_text: str, request: GenerationRequest) -> str:
        """Post-process the generated output."""
        # Remove stop sequences
        for stop_seq in request.stop_sequences:
            if stop_seq in generated_text:
                generated_text = generated_text.split(stop_seq)[0]

        # Clean up common issues
        generated_text = generated_text.strip()

        # Remove incomplete lines at the end
        lines = generated_text.split('\n')
        if lines and not lines[-1].strip():
            lines = lines[:-1]

        return '\n'.join(lines)

    def suggest_completions(
        self,
        code_context: str,
        cursor_position: int,
        language: str = "python",
        max_suggestions: int = 3
    ) -> List[str]:
        """Suggest code completions at cursor position."""
        # Extract prefix and suffix
        prefix = code_context[:cursor_position]
        suffix = code_context[cursor_position:]

        # Create completion request
        request = GenerationRequest(
            prompt=prefix,
            language=language,
            max_tokens=128,
            temperature=0.2,  # Lower temperature for completions
        )

        suggestions = []
        for i in range(max_suggestions):
            try:
                # Vary temperature slightly for diversity
                request.temperature = 0.1 + (i * 0.1)
                response = self.generate_code(request)

                if response.generated_code and response.generated_code not in suggestions:
                    suggestions.append(response.generated_code)

            except Exception as e:
                self.logger.warning("Failed to generate completion",
                                  attempt=i+1, error=str(e))
                continue

        return suggestions

    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get information about available models."""
        models = []
        for model in model_registry.get_available_models():
            info = model_registry.get_model_info(model)
            info["is_loaded"] = self.model_manager.is_model_loaded(model)
            info["can_load"] = info["memory_requirements_gb"] <= self.model_manager.max_memory
            models.append(info)

        return models

    def set_default_model(self, model: SupportedModel) -> None:
        """Set the default model for generation."""
        self._default_model = model
        self.logger.info("Default model changed", model=model.value)

    def get_model_status(self) -> Dict[str, Any]:
        """Get current model status and capabilities."""
        return {
            "current_model": self.model_manager.get_current_model(),
            "default_model": self._default_model.value if self._default_model else None,
            "device": self.model_manager.device,
            "max_memory_gb": self.model_manager.max_memory,
            "loaded_models": list(self.model_manager._loaded_models.keys()),
        }
