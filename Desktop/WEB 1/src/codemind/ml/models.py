"""
Model definitions and configurations for CodeMind.

This module defines the supported models, their configurations,
and model-specific settings for code generation tasks.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path

from ..core.logging import LoggerMixin


class SupportedModel(Enum):
    """Supported ML models for code generation."""
    
    # CodeLlama models
    CODELLAMA_7B = "codellama/CodeLlama-7b-Python-hf"
    CODELLAMA_13B = "codellama/CodeLlama-13b-Python-hf"
    CODELLAMA_34B = "codellama/CodeLlama-34b-Python-hf"
    
    # Microsoft CodeGPT
    CODEGPT_SMALL = "microsoft/CodeGPT-small-py"
    CODEGPT_MEDIUM = "microsoft/CodeGPT-small-py-adaptedGPT2"
    
    # Salesforce CodeGen
    CODEGEN_350M = "Salesforce/codegen-350M-mono"
    CODEGEN_2B = "Salesforce/codegen-2B-mono"
    CODEGEN_6B = "Salesforce/codegen-6B-mono"
    
    # StarCoder
    STARCODER_BASE = "bigcode/starcoder"
    STARCODER_15B = "bigcode/starcoderbase-15b"
    
    # Custom/Local models
    CUSTOM_LOCAL = "custom/local"


@dataclass
class ModelCapabilities:
    """Defines what a model can do."""
    
    supports_completion: bool = True
    supports_chat: bool = False
    supports_infill: bool = False
    supports_instruction: bool = False
    max_context_length: int = 2048
    supported_languages: List[str] = None
    
    def __post_init__(self):
        if self.supported_languages is None:
            self.supported_languages = ["python"]


@dataclass 
class ModelSpec:
    """Complete specification for a model."""
    
    name: str
    model_id: str
    capabilities: ModelCapabilities
    default_config: Dict[str, Any]
    memory_requirements_gb: float
    download_size_gb: float
    license: str = "Unknown"
    description: str = ""
    
    @property
    def is_local_compatible(self) -> bool:
        """Check if model can run locally on typical hardware."""
        return self.memory_requirements_gb <= 16.0


class ModelRegistry(LoggerMixin):
    """Registry of available models and their specifications."""
    
    def __init__(self):
        self._models = self._initialize_models()
    
    def _initialize_models(self) -> Dict[SupportedModel, ModelSpec]:
        """Initialize the model registry with specifications."""
        
        models = {}
        
        # CodeLlama models
        models[SupportedModel.CODELLAMA_7B] = ModelSpec(
            name="CodeLlama 7B Python",
            model_id="codellama/CodeLlama-7b-Python-hf",
            capabilities=ModelCapabilities(
                supports_completion=True,
                supports_chat=True,
                supports_instruction=True,
                max_context_length=4096,
                supported_languages=["python", "javascript", "typescript", "java", "cpp", "c"]
            ),
            default_config={
                "temperature": 0.1,
                "top_p": 0.9,
                "max_new_tokens": 512,
                "do_sample": True,
                "pad_token_id": 0,
            },
            memory_requirements_gb=14.0,
            download_size_gb=13.0,
            license="Custom (Llama 2)",
            description="7B parameter CodeLlama model fine-tuned for Python"
        )
        
        models[SupportedModel.CODELLAMA_13B] = ModelSpec(
            name="CodeLlama 13B Python", 
            model_id="codellama/CodeLlama-13b-Python-hf",
            capabilities=ModelCapabilities(
                supports_completion=True,
                supports_chat=True,
                supports_instruction=True,
                max_context_length=4096,
                supported_languages=["python", "javascript", "typescript", "java", "cpp", "c"]
            ),
            default_config={
                "temperature": 0.1,
                "top_p": 0.9,
                "max_new_tokens": 512,
                "do_sample": True,
                "pad_token_id": 0,
            },
            memory_requirements_gb=26.0,
            download_size_gb=25.0,
            license="Custom (Llama 2)",
            description="13B parameter CodeLlama model fine-tuned for Python"
        )
        
        # Microsoft CodeGPT
        models[SupportedModel.CODEGPT_SMALL] = ModelSpec(
            name="CodeGPT Small Python",
            model_id="microsoft/CodeGPT-small-py",
            capabilities=ModelCapabilities(
                supports_completion=True,
                max_context_length=1024,
                supported_languages=["python"]
            ),
            default_config={
                "temperature": 0.2,
                "top_p": 0.95,
                "max_new_tokens": 256,
                "do_sample": True,
            },
            memory_requirements_gb=2.0,
            download_size_gb=0.5,
            license="MIT",
            description="Small GPT model fine-tuned for Python code generation"
        )
        
        # Salesforce CodeGen
        models[SupportedModel.CODEGEN_350M] = ModelSpec(
            name="CodeGen 350M Mono",
            model_id="Salesforce/codegen-350M-mono",
            capabilities=ModelCapabilities(
                supports_completion=True,
                max_context_length=2048,
                supported_languages=["python", "javascript", "java", "cpp", "c"]
            ),
            default_config={
                "temperature": 0.2,
                "top_p": 0.95,
                "max_new_tokens": 256,
                "do_sample": True,
            },
            memory_requirements_gb=1.5,
            download_size_gb=0.7,
            license="BSD-3-Clause",
            description="350M parameter CodeGen model for code generation"
        )
        
        models[SupportedModel.CODEGEN_2B] = ModelSpec(
            name="CodeGen 2B Mono",
            model_id="Salesforce/codegen-2B-mono", 
            capabilities=ModelCapabilities(
                supports_completion=True,
                max_context_length=2048,
                supported_languages=["python", "javascript", "java", "cpp", "c"]
            ),
            default_config={
                "temperature": 0.2,
                "top_p": 0.95,
                "max_new_tokens": 512,
                "do_sample": True,
            },
            memory_requirements_gb=8.0,
            download_size_gb=4.0,
            license="BSD-3-Clause",
            description="2B parameter CodeGen model for code generation"
        )
        
        # StarCoder
        models[SupportedModel.STARCODER_BASE] = ModelSpec(
            name="StarCoder Base",
            model_id="bigcode/starcoder",
            capabilities=ModelCapabilities(
                supports_completion=True,
                supports_infill=True,
                max_context_length=8192,
                supported_languages=[
                    "python", "javascript", "typescript", "java", "cpp", "c", 
                    "go", "rust", "php", "ruby", "swift", "kotlin"
                ]
            ),
            default_config={
                "temperature": 0.2,
                "top_p": 0.95,
                "max_new_tokens": 512,
                "do_sample": True,
            },
            memory_requirements_gb=32.0,
            download_size_gb=30.0,
            license="BigCode OpenRAIL-M",
            description="15B parameter model trained on code from many languages"
        )
        
        return models
    
    def get_model_spec(self, model: SupportedModel) -> Optional[ModelSpec]:
        """Get specification for a model."""
        return self._models.get(model)
    
    def get_available_models(self) -> List[SupportedModel]:
        """Get list of all available models."""
        return list(self._models.keys())
    
    def get_local_compatible_models(self, max_memory_gb: float = 16.0) -> List[SupportedModel]:
        """Get models that can run locally with given memory constraint."""
        compatible = []
        for model, spec in self._models.items():
            if spec.memory_requirements_gb <= max_memory_gb:
                compatible.append(model)
        return compatible
    
    def get_models_by_language(self, language: str) -> List[SupportedModel]:
        """Get models that support a specific programming language."""
        matching = []
        for model, spec in self._models.items():
            if language.lower() in [lang.lower() for lang in spec.capabilities.supported_languages]:
                matching.append(model)
        return matching
    
    def recommend_model(
        self, 
        language: str = "python",
        max_memory_gb: float = 8.0,
        prefer_quality: bool = True
    ) -> Optional[SupportedModel]:
        """Recommend a model based on constraints and preferences."""
        
        # Filter by language support
        candidates = self.get_models_by_language(language)
        
        # Filter by memory constraints
        candidates = [
            model for model in candidates 
            if self._models[model].memory_requirements_gb <= max_memory_gb
        ]
        
        if not candidates:
            return None
        
        # Sort by preference
        if prefer_quality:
            # Prefer larger models (better quality)
            candidates.sort(
                key=lambda m: self._models[m].memory_requirements_gb, 
                reverse=True
            )
        else:
            # Prefer smaller models (faster inference)
            candidates.sort(
                key=lambda m: self._models[m].memory_requirements_gb
            )
        
        return candidates[0]
    
    def get_model_info(self, model: SupportedModel) -> Dict[str, Any]:
        """Get detailed information about a model."""
        spec = self.get_model_spec(model)
        if not spec:
            return {}
        
        return {
            "name": spec.name,
            "model_id": spec.model_id,
            "description": spec.description,
            "license": spec.license,
            "memory_requirements_gb": spec.memory_requirements_gb,
            "download_size_gb": spec.download_size_gb,
            "max_context_length": spec.capabilities.max_context_length,
            "supported_languages": spec.capabilities.supported_languages,
            "supports_completion": spec.capabilities.supports_completion,
            "supports_chat": spec.capabilities.supports_chat,
            "supports_infill": spec.capabilities.supports_infill,
            "supports_instruction": spec.capabilities.supports_instruction,
            "is_local_compatible": spec.is_local_compatible,
        }


# Global model registry instance
model_registry = ModelRegistry()
