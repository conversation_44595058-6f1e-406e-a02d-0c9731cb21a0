"""
Spider web learning coordinator for CodeMind.

This module implements the distributed learning system where local nodes
can optionally share anonymized patterns with a master coordinator to
improve the global model while maintaining privacy.
"""

import uuid
import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json

try:
    import httpx
    import websockets
except ImportError:
    httpx = None
    websockets = None

from ..core.config import Config
from ..core.logging import LoggerMixin
from ..core.exceptions import NetworkError, SecurityError
from ..analysis.patterns import CodePattern


class NodeStatus(Enum):
    """Status of a learning node."""
    OFFLINE = "offline"
    CONNECTING = "connecting"
    ONLINE = "online"
    SYNCING = "syncing"
    ERROR = "error"


@dataclass
class LearningNode:
    """Represents a node in the spider web learning network."""
    
    node_id: str
    public_key: str
    last_seen: datetime
    status: NodeStatus = NodeStatus.OFFLINE
    patterns_shared: int = 0
    patterns_received: int = 0
    trust_score: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_active(self) -> bool:
        """Check if node is considered active."""
        return (
            self.status in [NodeStatus.ONLINE, NodeStatus.SYNCING] and
            datetime.now() - self.last_seen < timedelta(hours=24)
        )


@dataclass
class SyncRequest:
    """Request for pattern synchronization."""
    
    request_id: str
    node_id: str
    patterns: List[Dict[str, Any]]
    timestamp: datetime
    signature: Optional[str] = None
    
    @classmethod
    def create(cls, node_id: str, patterns: List[Dict[str, Any]]) -> "SyncRequest":
        """Create a new sync request."""
        return cls(
            request_id=str(uuid.uuid4()),
            node_id=node_id,
            patterns=patterns,
            timestamp=datetime.now()
        )


class SpiderCoordinator(LoggerMixin):
    """
    Coordinates the spider web learning network.
    
    This class manages the master node that receives anonymized patterns
    from local nodes and distributes improved models back to the network.
    """
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.node_id = self._generate_node_id()
        self.nodes: Dict[str, LearningNode] = {}
        self.pending_requests: Dict[str, SyncRequest] = {}
        self.pattern_cache: Dict[str, Any] = {}
        self._running = False
        
        # Network settings
        self.master_url = self.config.spider.master_node_url
        self.sync_interval = timedelta(hours=self.config.spider.sync_interval_hours)
        
        self.logger.info("SpiderCoordinator initialized", 
                        node_id=self.node_id,
                        federated_learning=self.config.spider.enable_federated_learning)
    
    def _generate_node_id(self) -> str:
        """Generate a unique node ID."""
        # Use a hash of system info for consistent ID
        import platform
        import getpass
        
        system_info = f"{platform.node()}-{getpass.getuser()}-{platform.system()}"
        return hashlib.sha256(system_info.encode()).hexdigest()[:16]
    
    async def start(self) -> None:
        """Start the spider coordinator."""
        if not self.config.spider.enable_federated_learning:
            self.logger.info("Federated learning disabled, coordinator not started")
            return
        
        if httpx is None:
            self.logger.warning("httpx not available, network features disabled")
            return
        
        self._running = True
        self.logger.info("Spider coordinator started")
        
        # Start background tasks
        asyncio.create_task(self._sync_loop())
        asyncio.create_task(self._cleanup_loop())
    
    async def stop(self) -> None:
        """Stop the spider coordinator."""
        self._running = False
        self.logger.info("Spider coordinator stopped")
    
    async def register_node(self, node: LearningNode) -> bool:
        """Register a new node in the network."""
        try:
            # Validate node
            if not self._validate_node(node):
                self.logger.warning("Node validation failed", node_id=node.node_id)
                return False
            
            # Add to registry
            self.nodes[node.node_id] = node
            node.status = NodeStatus.ONLINE
            node.last_seen = datetime.now()
            
            self.logger.info("Node registered", 
                           node_id=node.node_id,
                           total_nodes=len(self.nodes))
            
            return True
            
        except Exception as e:
            self.logger.error("Node registration failed", 
                            node_id=node.node_id, 
                            error=str(e))
            return False
    
    def _validate_node(self, node: LearningNode) -> bool:
        """Validate a node before registration."""
        # Basic validation
        if not node.node_id or not node.public_key:
            return False
        
        # Check if already registered
        if node.node_id in self.nodes:
            existing = self.nodes[node.node_id]
            # Allow re-registration with same public key
            return existing.public_key == node.public_key
        
        return True
    
    async def submit_patterns(
        self, 
        node_id: str, 
        patterns: List[CodePattern]
    ) -> bool:
        """Submit patterns from a local node."""
        try:
            if not self.config.spider.enable_federated_learning:
                self.logger.debug("Federated learning disabled")
                return False
            
            # Anonymize patterns
            anonymized = self._anonymize_patterns(patterns)
            
            # Create sync request
            request = SyncRequest.create(node_id, anonymized)
            
            # Store request
            self.pending_requests[request.request_id] = request
            
            # Update node stats
            if node_id in self.nodes:
                self.nodes[node_id].patterns_shared += len(patterns)
                self.nodes[node_id].last_seen = datetime.now()
            
            self.logger.info("Patterns submitted", 
                           node_id=node_id,
                           pattern_count=len(patterns))
            
            # Process patterns asynchronously
            asyncio.create_task(self._process_patterns(request))
            
            return True
            
        except Exception as e:
            self.logger.error("Pattern submission failed", 
                            node_id=node_id, 
                            error=str(e))
            return False
    
    def _anonymize_patterns(self, patterns: List[CodePattern]) -> List[Dict[str, Any]]:
        """Anonymize patterns before sharing."""
        anonymized = []
        
        for pattern in patterns:
            # Remove identifying information
            anon_pattern = {
                "type": pattern.type.value,
                "language": pattern.language.value,
                "pattern": pattern.pattern,
                "description": pattern.description,
                "frequency": min(pattern.frequency, 100),  # Cap frequency
                "confidence": pattern.confidence,
                # Remove file paths and specific examples
                "metadata": {
                    "anonymized": True,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
            # Add differential privacy noise if configured
            if self.config.spider.privacy_level == "high":
                anon_pattern["confidence"] = self._add_privacy_noise(
                    anon_pattern["confidence"]
                )
            
            anonymized.append(anon_pattern)
        
        return anonymized
    
    def _add_privacy_noise(self, value: float) -> float:
        """Add differential privacy noise to a value."""
        import random
        
        epsilon = self.config.spider.differential_privacy_epsilon
        sensitivity = 0.1  # Sensitivity for confidence scores
        
        # Laplace noise
        noise = random.laplace(0, sensitivity / epsilon)
        return max(0.0, min(1.0, value + noise))
    
    async def _process_patterns(self, request: SyncRequest) -> None:
        """Process submitted patterns."""
        try:
            # Store patterns in cache
            for pattern in request.patterns:
                pattern_hash = self._hash_pattern(pattern)
                self.pattern_cache[pattern_hash] = {
                    "pattern": pattern,
                    "node_id": request.node_id,
                    "timestamp": request.timestamp,
                    "processed": False
                }
            
            # Mark request as processed
            if request.request_id in self.pending_requests:
                del self.pending_requests[request.request_id]
            
            self.logger.debug("Patterns processed", 
                            request_id=request.request_id,
                            pattern_count=len(request.patterns))
            
        except Exception as e:
            self.logger.error("Pattern processing failed", 
                            request_id=request.request_id, 
                            error=str(e))
    
    def _hash_pattern(self, pattern: Dict[str, Any]) -> str:
        """Generate a hash for a pattern."""
        # Create a stable hash based on pattern content
        content = f"{pattern['type']}-{pattern['language']}-{pattern['pattern']}"
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    async def get_updated_patterns(self, node_id: str) -> List[Dict[str, Any]]:
        """Get updated patterns for a node."""
        try:
            if node_id not in self.nodes:
                return []
            
            # Get patterns that are newer than node's last sync
            node = self.nodes[node_id]
            cutoff_time = node.last_seen - self.sync_interval
            
            updated_patterns = []
            for pattern_hash, data in self.pattern_cache.items():
                if (data["timestamp"] > cutoff_time and 
                    data["node_id"] != node_id):  # Don't send back own patterns
                    updated_patterns.append(data["pattern"])
            
            # Update node stats
            node.patterns_received += len(updated_patterns)
            node.last_seen = datetime.now()
            
            self.logger.info("Updated patterns retrieved", 
                           node_id=node_id,
                           pattern_count=len(updated_patterns))
            
            return updated_patterns
            
        except Exception as e:
            self.logger.error("Pattern retrieval failed", 
                            node_id=node_id, 
                            error=str(e))
            return []
    
    async def _sync_loop(self) -> None:
        """Background loop for synchronization."""
        while self._running:
            try:
                await self._sync_with_master()
                await asyncio.sleep(self.sync_interval.total_seconds())
            except Exception as e:
                self.logger.error("Sync loop error", error=str(e))
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _sync_with_master(self) -> None:
        """Synchronize with master node."""
        if not self.master_url or not httpx:
            return
        
        try:
            async with httpx.AsyncClient() as client:
                # Send heartbeat
                response = await client.post(
                    f"{self.master_url}/api/heartbeat",
                    json={
                        "node_id": self.node_id,
                        "timestamp": datetime.now().isoformat(),
                        "pattern_count": len(self.pattern_cache),
                        "active_nodes": len([n for n in self.nodes.values() if n.is_active])
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    self.logger.debug("Heartbeat sent successfully")
                else:
                    self.logger.warning("Heartbeat failed", status=response.status_code)
                    
        except Exception as e:
            self.logger.warning("Master sync failed", error=str(e))
    
    async def _cleanup_loop(self) -> None:
        """Background loop for cleanup tasks."""
        while self._running:
            try:
                await self._cleanup_inactive_nodes()
                await self._cleanup_old_patterns()
                await asyncio.sleep(3600)  # Run every hour
            except Exception as e:
                self.logger.error("Cleanup loop error", error=str(e))
    
    async def _cleanup_inactive_nodes(self) -> None:
        """Remove inactive nodes."""
        cutoff_time = datetime.now() - timedelta(days=7)
        
        inactive_nodes = [
            node_id for node_id, node in self.nodes.items()
            if node.last_seen < cutoff_time
        ]
        
        for node_id in inactive_nodes:
            del self.nodes[node_id]
            self.logger.info("Removed inactive node", node_id=node_id)
    
    async def _cleanup_old_patterns(self) -> None:
        """Remove old patterns from cache."""
        cutoff_time = datetime.now() - timedelta(
            days=self.config.spider.max_pattern_age_days
        )
        
        old_patterns = [
            pattern_hash for pattern_hash, data in self.pattern_cache.items()
            if data["timestamp"] < cutoff_time
        ]
        
        for pattern_hash in old_patterns:
            del self.pattern_cache[pattern_hash]
        
        if old_patterns:
            self.logger.info("Cleaned up old patterns", count=len(old_patterns))
    
    def get_network_status(self) -> Dict[str, Any]:
        """Get current network status."""
        active_nodes = [n for n in self.nodes.values() if n.is_active]
        
        return {
            "node_id": self.node_id,
            "total_nodes": len(self.nodes),
            "active_nodes": len(active_nodes),
            "cached_patterns": len(self.pattern_cache),
            "pending_requests": len(self.pending_requests),
            "federated_learning_enabled": self.config.spider.enable_federated_learning,
            "master_url": self.master_url,
            "last_sync": datetime.now().isoformat(),
        }
    
    def get_node_stats(self, node_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific node."""
        if node_id not in self.nodes:
            return None
        
        node = self.nodes[node_id]
        return {
            "node_id": node.node_id,
            "status": node.status.value,
            "last_seen": node.last_seen.isoformat(),
            "patterns_shared": node.patterns_shared,
            "patterns_received": node.patterns_received,
            "trust_score": node.trust_score,
            "is_active": node.is_active,
        }
