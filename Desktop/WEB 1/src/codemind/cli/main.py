"""
Main CLI application for CodeMind.

This module defines the primary command-line interface using Typer,
providing a rich, interactive experience for users.
"""

import sys
from pathlib import Path
from typing import Optional, List
import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Confirm

from ..core import Config, setup_logging, get_logger, CodeMindError
from ..core.logging import LoggerMixin

# Create the main Typer app
app = typer.Typer(
    name="codemind",
    help="🧠 CodeMind - The Self-Evolving Code Assistant",
    add_completion=False,
    rich_markup_mode="rich",
)

# Global console instance
console = Console()


class CLIManager(LoggerMixin):
    """Manages CLI operations and state."""
    
    def __init__(self):
        self.config = Config()
        self.console = console
        setup_logging(self.config, self.console)
        self.logger.info("CodeMind CLI initialized", version=self.config.version)
    
    def ensure_initialized(self) -> bool:
        """Check if CodeMind is initialized in the current directory."""
        codemind_dir = Path.cwd() / ".codemind"
        return codemind_dir.exists() and (codemind_dir / "config.yaml").exists()


# Global CLI manager instance
cli_manager = CLIManager()


@app.command()
def init(
    force: bool = typer.Option(False, "--force", "-f", help="Force initialization even if already initialized"),
    interactive: bool = typer.Option(True, "--interactive/--no-interactive", help="Run in interactive mode"),
) -> None:
    """
    🚀 Initialize CodeMind in the current directory.
    
    This sets up the local configuration, creates necessary directories,
    and prepares the codebase for analysis and learning.
    """
    try:
        current_dir = Path.cwd()
        codemind_dir = current_dir / ".codemind"
        
        if codemind_dir.exists() and not force:
            console.print("❌ CodeMind is already initialized in this directory.", style="red")
            console.print("Use --force to reinitialize.", style="yellow")
            raise typer.Exit(1)
        
        if interactive:
            console.print(Panel.fit(
                "🧠 [bold blue]Welcome to CodeMind![/bold blue]\n\n"
                "This will initialize CodeMind in your current directory.\n"
                "CodeMind will analyze your codebase and learn from your patterns.",
                title="Initialization"
            ))
            
            if not Confirm.ask("Continue with initialization?"):
                console.print("Initialization cancelled.", style="yellow")
                raise typer.Exit(0)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Initializing CodeMind...", total=None)
            
            # Create directories
            progress.update(task, description="Creating directories...")
            codemind_dir.mkdir(exist_ok=True)
            (codemind_dir / "models").mkdir(exist_ok=True)
            (codemind_dir / "cache").mkdir(exist_ok=True)
            (codemind_dir / "patterns").mkdir(exist_ok=True)
            
            # Create local config
            progress.update(task, description="Creating configuration...")
            local_config = cli_manager.config.copy()
            local_config.save_to_file(codemind_dir / "config.yaml")
            
            # Create gitignore
            progress.update(task, description="Setting up git integration...")
            gitignore_path = codemind_dir / ".gitignore"
            with open(gitignore_path, 'w') as f:
                f.write("# CodeMind local files\n")
                f.write("cache/\n")
                f.write("models/\n")
                f.write("*.log\n")
            
            progress.update(task, description="Initialization complete!")
        
        console.print("✅ [bold green]CodeMind initialized successfully![/bold green]")
        console.print("\n📋 Next steps:")
        console.print("  • Run [bold cyan]codemind analyze[/bold cyan] to analyze your codebase")
        console.print("  • Run [bold cyan]codemind status[/bold cyan] to check system status")
        console.print("  • Run [bold cyan]codemind suggest --help[/bold cyan] to see suggestion options")
        
    except Exception as e:
        cli_manager.logger.error("Initialization failed", error=str(e))
        console.print(f"❌ Initialization failed: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def status() -> None:
    """
    📊 Show CodeMind system status and health information.
    """
    try:
        if not cli_manager.ensure_initialized():
            console.print("❌ CodeMind is not initialized in this directory.", style="red")
            console.print("Run [bold cyan]codemind init[/bold cyan] to get started.", style="yellow")
            raise typer.Exit(1)
        
        # Create status table
        table = Table(title="🧠 CodeMind Status", show_header=True, header_style="bold blue")
        table.add_column("Component", style="cyan", no_wrap=True)
        table.add_column("Status", style="green")
        table.add_column("Details", style="white")
        
        # Check various components
        table.add_row("Configuration", "✅ Loaded", f"Version {cli_manager.config.version}")
        table.add_row("Local Directory", "✅ Initialized", str(Path.cwd() / ".codemind"))
        table.add_row("Models", "⏳ Not Downloaded", "Run 'codemind download' to get models")
        table.add_row("Analysis Engine", "✅ Ready", "Tree-sitter parsers available")
        table.add_row("Spider Network", "❌ Disabled", "Federated learning not enabled")
        
        console.print(table)
        
        # Show configuration summary
        config_panel = Panel(
            f"[bold]Data Directory:[/bold] {cli_manager.config.data_path}\n"
            f"[bold]Log Level:[/bold] {cli_manager.config.log_level}\n"
            f"[bold]Auto Learning:[/bold] {'Enabled' if cli_manager.config.auto_learn else 'Disabled'}\n"
            f"[bold]Debug Mode:[/bold] {'Enabled' if cli_manager.config.debug else 'Disabled'}",
            title="Configuration",
            border_style="blue"
        )
        console.print(config_panel)
        
    except Exception as e:
        cli_manager.logger.error("Status check failed", error=str(e))
        console.print(f"❌ Status check failed: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def analyze(
    path: Optional[Path] = typer.Argument(None, help="Path to analyze (defaults to current directory)"),
    recursive: bool = typer.Option(True, "--recursive/--no-recursive", help="Analyze subdirectories"),
    languages: Optional[List[str]] = typer.Option(None, "--lang", help="Specific languages to analyze"),
) -> None:
    """
    🔍 Analyze codebase and extract patterns.
    
    This command analyzes your codebase to understand its structure,
    patterns, and coding style for better suggestions.
    """
    try:
        if not cli_manager.ensure_initialized():
            console.print("❌ CodeMind is not initialized in this directory.", style="red")
            console.print("Run [bold cyan]codemind init[/bold cyan] to get started.", style="yellow")
            raise typer.Exit(1)
        
        target_path = path or Path.cwd()
        
        console.print(f"🔍 Analyzing codebase at: [bold cyan]{target_path}[/bold cyan]")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Scanning files...", total=None)
            
            # TODO: Implement actual analysis
            progress.update(task, description="Parsing source files...")
            progress.update(task, description="Extracting patterns...")
            progress.update(task, description="Building knowledge graph...")
            progress.update(task, description="Analysis complete!")
        
        console.print("✅ [bold green]Analysis completed successfully![/bold green]")
        console.print("\n📊 Results:")
        console.print("  • Files analyzed: [bold]42[/bold]")
        console.print("  • Patterns found: [bold]15[/bold]")
        console.print("  • Languages detected: [bold]Python, JavaScript[/bold]")
        
    except Exception as e:
        cli_manager.logger.error("Analysis failed", error=str(e))
        console.print(f"❌ Analysis failed: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def suggest(
    feature: str = typer.Argument(..., help="Feature or functionality to suggest"),
    context: Optional[str] = typer.Option(None, "--context", "-c", help="Additional context"),
    file_path: Optional[Path] = typer.Option(None, "--file", "-f", help="Specific file to focus on"),
) -> None:
    """
    💡 Get AI-powered code suggestions.
    
    Generate intelligent code suggestions based on your codebase patterns
    and the requested feature or functionality.
    """
    try:
        if not cli_manager.ensure_initialized():
            console.print("❌ CodeMind is not initialized in this directory.", style="red")
            console.print("Run [bold cyan]codemind init[/bold cyan] to get started.", style="yellow")
            raise typer.Exit(1)
        
        console.print(f"💡 Generating suggestions for: [bold cyan]{feature}[/bold cyan]")
        
        if context:
            console.print(f"📝 Context: {context}")
        
        if file_path:
            console.print(f"📁 Target file: {file_path}")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Analyzing request...", total=None)
            
            # TODO: Implement actual suggestion generation
            progress.update(task, description="Loading models...")
            progress.update(task, description="Analyzing codebase context...")
            progress.update(task, description="Generating suggestions...")
            progress.update(task, description="Suggestions ready!")
        
        # Mock suggestion output
        suggestion_panel = Panel(
            "```python\n"
            "# Suggested implementation for authentication system\n"
            "from flask_login import LoginManager, login_required\n\n"
            "def setup_auth(app):\n"
            "    login_manager = LoginManager()\n"
            "    login_manager.init_app(app)\n"
            "    return login_manager\n"
            "```\n\n"
            "[bold]Explanation:[/bold] Based on your Flask project structure, "
            "this implements a basic authentication system using Flask-Login.",
            title="💡 Code Suggestion",
            border_style="green"
        )
        console.print(suggestion_panel)
        
    except Exception as e:
        cli_manager.logger.error("Suggestion generation failed", error=str(e))
        console.print(f"❌ Suggestion generation failed: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def learn(
    from_git: bool = typer.Option(False, "--from-git", help="Learn from git history"),
    from_files: Optional[List[Path]] = typer.Option(None, "--file", help="Learn from specific files"),
) -> None:
    """
    🎓 Learn from code changes and patterns.
    
    Analyze recent changes and update the local model with new patterns
    and coding preferences.
    """
    try:
        if not cli_manager.ensure_initialized():
            console.print("❌ CodeMind is not initialized in this directory.", style="red")
            console.print("Run [bold cyan]codemind init[/bold cyan] to get started.", style="yellow")
            raise typer.Exit(1)
        
        console.print("🎓 Starting learning process...")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Preparing learning...", total=None)
            
            if from_git:
                progress.update(task, description="Analyzing git history...")
                # TODO: Implement git history analysis
            
            if from_files:
                progress.update(task, description="Analyzing specified files...")
                # TODO: Implement file analysis
            
            progress.update(task, description="Extracting patterns...")
            progress.update(task, description="Updating local model...")
            progress.update(task, description="Learning complete!")
        
        console.print("✅ [bold green]Learning completed successfully![/bold green]")
        console.print("\n📈 Learning Summary:")
        console.print("  • New patterns learned: [bold]7[/bold]")
        console.print("  • Model accuracy improved: [bold]+2.3%[/bold]")
        console.print("  • Knowledge base updated: [bold]Yes[/bold]")
        
    except Exception as e:
        cli_manager.logger.error("Learning failed", error=str(e))
        console.print(f"❌ Learning failed: {e}", style="red")
        raise typer.Exit(1)


@app.command()
def version() -> None:
    """Show CodeMind version information."""
    console.print(f"🧠 CodeMind v{cli_manager.config.version}")
    console.print("The Self-Evolving Code Assistant")
    console.print("Licensed under AGPL-3.0")


if __name__ == "__main__":
    app()
