"""
Exception classes for CodeMind.

This module defines the exception hierarchy used throughout the CodeMind system.
All exceptions inherit from CodeMindError to provide a consistent interface.
"""

from typing import Optional, Dict, Any


class CodeMindError(Exception):
    """Base exception class for all CodeMind errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class SecurityError(CodeMindError):
    """Raised when security-related operations fail."""
    pass


class ModelError(CodeMindError):
    """Raised when ML model operations fail."""
    pass


class AnalysisError(CodeMindError):
    """Raised when code analysis operations fail."""
    pass


class StorageError(CodeMindError):
    """Raised when storage operations fail."""
    pass


class NetworkError(CodeMindError):
    """Raised when network operations fail."""
    pass


class ConfigurationError(CodeMindError):
    """Raised when configuration is invalid."""
    pass


class ValidationError(CodeMindError):
    """Raised when data validation fails."""
    pass
