"""
Configuration management for CodeMind.

This module handles all configuration loading, validation, and management
for the CodeMind system, including user preferences, model settings,
and security configurations.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, validator
import toml
import yaml


class ModelConfig(BaseModel):
    """Configuration for ML models."""
    
    name: str = "microsoft/CodeGPT-small-py"
    max_length: int = 2048
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 50
    device: str = "auto"  # auto, cpu, cuda, mps
    quantization: Optional[str] = "4bit"  # None, 4bit, 8bit
    cache_dir: Optional[str] = None
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('Temperature must be between 0.0 and 2.0')
        return v
    
    @validator('top_p')
    def validate_top_p(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('top_p must be between 0.0 and 1.0')
        return v


class SecurityConfig(BaseModel):
    """Configuration for security settings."""
    
    enable_encryption: bool = True
    key_derivation_iterations: int = 100000
    signature_algorithm: str = "Ed25519"
    encryption_algorithm: str = "ChaCha20Poly1305"
    secure_delete: bool = True
    audit_logging: bool = True
    
    
class SpiderConfig(BaseModel):
    """Configuration for the spider web learning system."""
    
    enable_federated_learning: bool = False
    master_node_url: Optional[str] = None
    sync_interval_hours: int = 24
    max_pattern_age_days: int = 30
    privacy_level: str = "high"  # low, medium, high, paranoid
    differential_privacy_epsilon: float = 1.0
    
    @validator('privacy_level')
    def validate_privacy_level(cls, v):
        if v not in ["low", "medium", "high", "paranoid"]:
            raise ValueError('Privacy level must be one of: low, medium, high, paranoid')
        return v


class AnalysisConfig(BaseModel):
    """Configuration for code analysis."""
    
    supported_languages: List[str] = [
        "python", "javascript", "typescript", "rust", "go", "java", "cpp", "c"
    ]
    max_file_size_mb: int = 10
    ignore_patterns: List[str] = [
        "*.pyc", "*.pyo", "*.pyd", "__pycache__", ".git", ".svn", 
        "node_modules", "target", "build", "dist"
    ]
    enable_semantic_analysis: bool = True
    enable_security_scanning: bool = True


class Config(BaseModel):
    """Main configuration class for CodeMind."""
    
    # Core settings
    version: str = "0.1.0"
    debug: bool = False
    log_level: str = "INFO"
    data_dir: str = "~/.codemind"
    
    # Component configurations
    model: ModelConfig = Field(default_factory=ModelConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    spider: SpiderConfig = Field(default_factory=SpiderConfig)
    analysis: AnalysisConfig = Field(default_factory=AnalysisConfig)
    
    # User preferences
    user_id: Optional[str] = None
    team_id: Optional[str] = None
    preferred_editor: str = "vscode"
    auto_learn: bool = True
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @property
    def data_path(self) -> Path:
        """Get the expanded data directory path."""
        return Path(self.data_dir).expanduser()
    
    @property
    def models_path(self) -> Path:
        """Get the models directory path."""
        return self.data_path / "models"
    
    @property
    def cache_path(self) -> Path:
        """Get the cache directory path."""
        return self.data_path / "cache"
    
    @property
    def logs_path(self) -> Path:
        """Get the logs directory path."""
        return self.data_path / "logs"
    
    def ensure_directories(self) -> None:
        """Ensure all required directories exist."""
        for path in [self.data_path, self.models_path, self.cache_path, self.logs_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def load_from_file(cls, config_path: Path) -> "Config":
        """Load configuration from a file."""
        if not config_path.exists():
            return cls()
        
        suffix = config_path.suffix.lower()
        
        try:
            if suffix == ".json":
                with open(config_path, 'r') as f:
                    data = json.load(f)
            elif suffix in [".yaml", ".yml"]:
                with open(config_path, 'r') as f:
                    data = yaml.safe_load(f)
            elif suffix == ".toml":
                with open(config_path, 'r') as f:
                    data = toml.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {suffix}")
            
            return cls(**data)
        except Exception as e:
            raise ValueError(f"Failed to load config from {config_path}: {e}")
    
    def save_to_file(self, config_path: Path) -> None:
        """Save configuration to a file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        suffix = config_path.suffix.lower()
        data = self.dict()
        
        if suffix == ".json":
            with open(config_path, 'w') as f:
                json.dump(data, f, indent=2)
        elif suffix in [".yaml", ".yml"]:
            with open(config_path, 'w') as f:
                yaml.dump(data, f, default_flow_style=False)
        elif suffix == ".toml":
            with open(config_path, 'w') as f:
                toml.dump(data, f)
        else:
            raise ValueError(f"Unsupported config file format: {suffix}")
    
    @classmethod
    def get_default_config_path(cls) -> Path:
        """Get the default configuration file path."""
        return Path("~/.codemind/config.yaml").expanduser()
    
    @classmethod
    def load_default(cls) -> "Config":
        """Load configuration from the default location."""
        return cls.load_from_file(cls.get_default_config_path())


def get_config() -> Config:
    """Get the global configuration instance."""
    return Config.load_default()
