"""
Logging configuration for CodeMind.

This module sets up structured logging using structlog with rich formatting
for development and JSON formatting for production.
"""

import sys
import logging
from pathlib import Path
from typing import Optional
import structlog
from rich.logging import <PERSON><PERSON>andler
from rich.console import Console

from .config import Config


def setup_logging(
    config: Optional[Config] = None,
    console: Optional[Console] = None
) -> None:
    """
    Set up logging configuration for CodeMind.
    
    Args:
        config: Configuration object. If None, uses default config.
        console: Rich console instance. If None, creates a new one.
    """
    if config is None:
        config = Config()
    
    if console is None:
        console = Console()
    
    # Ensure log directory exists
    config.ensure_directories()
    
    # Configure standard library logging
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        format="%(message)s",
        handlers=[
            RichHandler(
                console=console,
                show_time=True,
                show_path=True,
                rich_tracebacks=True,
                tracebacks_show_locals=config.debug,
            )
        ],
    )
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if config.debug:
        # Development mode: use rich formatting
        processors.append(structlog.dev.ConsoleRenderer())
    else:
        # Production mode: use JSON formatting
        processors.append(structlog.processors.JSONRenderer())
    
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # Set up file logging for important events
    file_handler = logging.FileHandler(config.logs_path / "codemind.log")
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    
    # Add file handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)
    
    # Suppress noisy third-party loggers
    logging.getLogger("transformers").setLevel(logging.WARNING)
    logging.getLogger("torch").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name, typically __name__
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get a logger instance for this class."""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)
