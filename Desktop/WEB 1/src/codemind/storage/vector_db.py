"""
Vector database integration for CodeMind.

This module provides semantic search and storage capabilities
using ChromaDB for code patterns and embeddings.
"""

import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

try:
    import chromadb
    from chromadb.config import Settings
    from chromadb.utils import embedding_functions
except ImportError:
    chromadb = None

from ..core.config import Config
from ..core.logging import LoggerMixin
from ..core.exceptions import StorageError
from ..analysis.patterns import CodePattern


@dataclass
class CodeEmbedding:
    """Represents a code embedding with metadata."""
    
    id: str
    code: str
    embedding: List[float]
    language: str
    file_path: str
    metadata: Dict[str, Any]


class VectorDatabase(LoggerMixin):
    """Vector database for semantic code search and pattern storage."""
    
    def __init__(self, config: Optional[Config] = None):
        self.config = config or Config()
        self.client = None
        self.collection = None
        self._initialize_db()
    
    def _initialize_db(self) -> None:
        """Initialize ChromaDB client and collection."""
        if chromadb is None:
            self.logger.warning("ChromaDB not available, vector search disabled")
            return
        
        try:
            # Create persistent client
            db_path = self.config.data_path / "vector_db"
            db_path.mkdir(parents=True, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=str(db_path),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Create or get collection
            embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name="all-MiniLM-L6-v2"  # Lightweight model
            )
            
            self.collection = self.client.get_or_create_collection(
                name="code_patterns",
                embedding_function=embedding_function,
                metadata={"description": "Code patterns and embeddings"}
            )
            
            self.logger.info("Vector database initialized", 
                           collection_size=self.collection.count())
            
        except Exception as e:
            self.logger.error("Failed to initialize vector database", error=str(e))
            self.client = None
            self.collection = None
    
    def add_code_pattern(self, pattern: CodePattern, code_snippet: str) -> bool:
        """Add a code pattern to the vector database."""
        if not self.collection:
            return False
        
        try:
            pattern_id = str(uuid.uuid4())
            
            # Prepare document and metadata
            document = f"{pattern.description}\n\n{code_snippet}"
            metadata = {
                "pattern_type": pattern.type.value,
                "language": pattern.language.value,
                "pattern_name": pattern.pattern,
                "confidence": pattern.confidence,
                "frequency": pattern.frequency,
                "file_paths": list(pattern.file_paths)[:10]  # Limit file paths
            }
            
            # Add to collection
            self.collection.add(
                documents=[document],
                metadatas=[metadata],
                ids=[pattern_id]
            )
            
            self.logger.debug("Pattern added to vector database", 
                            pattern_id=pattern_id,
                            pattern_type=pattern.type.value)
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to add pattern to vector database", error=str(e))
            return False
    
    def search_similar_patterns(
        self, 
        query: str, 
        language: Optional[str] = None,
        pattern_type: Optional[str] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search for similar code patterns."""
        if not self.collection:
            return []
        
        try:
            # Build where clause for filtering
            where_clause = {}
            if language:
                where_clause["language"] = language
            if pattern_type:
                where_clause["pattern_type"] = pattern_type
            
            # Perform semantic search
            results = self.collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            patterns = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    pattern = {
                        "document": doc,
                        "metadata": results["metadatas"][0][i],
                        "similarity": 1.0 - results["distances"][0][i],  # Convert distance to similarity
                        "id": results["ids"][0][i] if "ids" in results else None
                    }
                    patterns.append(pattern)
            
            self.logger.debug("Pattern search completed", 
                            query=query[:50],
                            results_count=len(patterns))
            
            return patterns
            
        except Exception as e:
            self.logger.error("Pattern search failed", query=query, error=str(e))
            return []
    
    def add_code_embedding(self, embedding: CodeEmbedding) -> bool:
        """Add a code embedding to the database."""
        if not self.collection:
            return False
        
        try:
            self.collection.add(
                documents=[embedding.code],
                metadatas=[{
                    "language": embedding.language,
                    "file_path": embedding.file_path,
                    **embedding.metadata
                }],
                ids=[embedding.id],
                embeddings=[embedding.embedding] if embedding.embedding else None
            )
            
            return True
            
        except Exception as e:
            self.logger.error("Failed to add code embedding", error=str(e))
            return False
    
    def search_similar_code(
        self, 
        code: str, 
        language: Optional[str] = None,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Search for similar code snippets."""
        if not self.collection:
            return []
        
        try:
            where_clause = {"language": language} if language else None
            
            results = self.collection.query(
                query_texts=[code],
                n_results=limit,
                where=where_clause,
                include=["documents", "metadatas", "distances"]
            )
            
            similar_code = []
            if results["documents"] and results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    similar = {
                        "code": doc,
                        "metadata": results["metadatas"][0][i],
                        "similarity": 1.0 - results["distances"][0][i],
                    }
                    similar_code.append(similar)
            
            return similar_code
            
        except Exception as e:
            self.logger.error("Code search failed", error=str(e))
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector database collection."""
        if not self.collection:
            return {"status": "unavailable"}
        
        try:
            count = self.collection.count()
            
            # Get sample of metadata to analyze
            sample = self.collection.get(limit=100, include=["metadatas"])
            
            languages = {}
            pattern_types = {}
            
            if sample["metadatas"]:
                for metadata in sample["metadatas"]:
                    lang = metadata.get("language", "unknown")
                    languages[lang] = languages.get(lang, 0) + 1
                    
                    ptype = metadata.get("pattern_type", "unknown")
                    pattern_types[ptype] = pattern_types.get(ptype, 0) + 1
            
            return {
                "status": "available",
                "total_patterns": count,
                "languages": languages,
                "pattern_types": pattern_types,
                "collection_name": self.collection.name
            }
            
        except Exception as e:
            self.logger.error("Failed to get collection stats", error=str(e))
            return {"status": "error", "error": str(e)}
    
    def clear_collection(self) -> bool:
        """Clear all data from the collection."""
        if not self.collection:
            return False
        
        try:
            # Get all IDs and delete them
            all_data = self.collection.get()
            if all_data["ids"]:
                self.collection.delete(ids=all_data["ids"])
            
            self.logger.info("Vector database collection cleared")
            return True
            
        except Exception as e:
            self.logger.error("Failed to clear collection", error=str(e))
            return False
    
    def backup_collection(self, backup_path: Path) -> bool:
        """Backup the collection to a file."""
        if not self.collection:
            return False
        
        try:
            import json
            
            # Get all data
            all_data = self.collection.get(include=["documents", "metadatas", "embeddings"])
            
            # Save to JSON
            backup_data = {
                "collection_name": self.collection.name,
                "data": all_data,
                "timestamp": str(datetime.now())
            }
            
            with open(backup_path, 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            self.logger.info("Collection backed up", backup_path=str(backup_path))
            return True
            
        except Exception as e:
            self.logger.error("Backup failed", error=str(e))
            return False
    
    def restore_collection(self, backup_path: Path) -> bool:
        """Restore collection from a backup file."""
        if not self.collection:
            return False
        
        try:
            import json
            from datetime import datetime
            
            with open(backup_path, 'r') as f:
                backup_data = json.load(f)
            
            data = backup_data["data"]
            
            if data["ids"]:
                self.collection.add(
                    documents=data["documents"],
                    metadatas=data["metadatas"],
                    ids=data["ids"],
                    embeddings=data.get("embeddings")
                )
            
            self.logger.info("Collection restored", backup_path=str(backup_path))
            return True
            
        except Exception as e:
            self.logger.error("Restore failed", error=str(e))
            return False
