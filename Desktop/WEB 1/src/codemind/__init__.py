"""
CodeMind - The Self-Evolving Code Assistant

An autonomous CLI tool that learns from codebases it interacts with,
improving its capabilities through continuous self-modification while
maintaining operational integrity.
"""

__version__ = "0.1.0"
__author__ = "CodeMind Team"
__email__ = "<EMAIL>"
__license__ = "AGPL-3.0"

from .core.config import Config
from .core.exceptions import CodeMindError

__all__ = ["Config", "CodeMindError", "__version__"]
