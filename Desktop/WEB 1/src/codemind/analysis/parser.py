"""
Code parsing and language detection using tree-sitter.

This module provides functionality to parse source code files into ASTs
and detect programming languages based on file extensions and content.
"""

import re
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    import tree_sitter
    from tree_sitter import Language, Parser, Node
except ImportError:
    tree_sitter = None
    Language = None
    Parser = None
    Node = None

from ..core.logging import LoggerMixin
from ..core.exceptions import AnalysisError


class SupportedLanguage(Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    RUST = "rust"
    GO = "go"
    JAVA = "java"
    CPP = "cpp"
    C = "c"
    HTML = "html"
    CSS = "css"
    JSON = "json"
    YAML = "yaml"
    MARKDOWN = "markdown"


@dataclass
class ParsedFile:
    """Represents a parsed source code file."""
    path: Path
    language: SupportedLanguage
    content: str
    ast: Optional[Any] = None  # tree_sitter.Node
    encoding: str = "utf-8"
    size_bytes: int = 0
    line_count: int = 0
    
    def __post_init__(self):
        if self.content:
            self.size_bytes = len(self.content.encode(self.encoding))
            self.line_count = len(self.content.splitlines())


class LanguageDetector(LoggerMixin):
    """Detects programming languages from file paths and content."""
    
    # File extension mappings
    EXTENSION_MAP = {
        '.py': SupportedLanguage.PYTHON,
        '.pyw': SupportedLanguage.PYTHON,
        '.js': SupportedLanguage.JAVASCRIPT,
        '.jsx': SupportedLanguage.JAVASCRIPT,
        '.mjs': SupportedLanguage.JAVASCRIPT,
        '.ts': SupportedLanguage.TYPESCRIPT,
        '.tsx': SupportedLanguage.TYPESCRIPT,
        '.rs': SupportedLanguage.RUST,
        '.go': SupportedLanguage.GO,
        '.java': SupportedLanguage.JAVA,
        '.cpp': SupportedLanguage.CPP,
        '.cxx': SupportedLanguage.CPP,
        '.cc': SupportedLanguage.CPP,
        '.c': SupportedLanguage.C,
        '.h': SupportedLanguage.C,
        '.hpp': SupportedLanguage.CPP,
        '.html': SupportedLanguage.HTML,
        '.htm': SupportedLanguage.HTML,
        '.css': SupportedLanguage.CSS,
        '.json': SupportedLanguage.JSON,
        '.yaml': SupportedLanguage.YAML,
        '.yml': SupportedLanguage.YAML,
        '.md': SupportedLanguage.MARKDOWN,
        '.markdown': SupportedLanguage.MARKDOWN,
    }
    
    # Shebang patterns
    SHEBANG_PATTERNS = {
        r'#!/.*python': SupportedLanguage.PYTHON,
        r'#!/.*node': SupportedLanguage.JAVASCRIPT,
        r'#!/.*bash': SupportedLanguage.MARKDOWN,  # Shell scripts as markdown for now
        r'#!/.*sh': SupportedLanguage.MARKDOWN,
    }
    
    def detect_from_path(self, path: Path) -> Optional[SupportedLanguage]:
        """Detect language from file path."""
        suffix = path.suffix.lower()
        return self.EXTENSION_MAP.get(suffix)
    
    def detect_from_content(self, content: str) -> Optional[SupportedLanguage]:
        """Detect language from file content."""
        lines = content.splitlines()
        if not lines:
            return None
        
        # Check shebang
        first_line = lines[0].strip()
        if first_line.startswith('#!'):
            for pattern, language in self.SHEBANG_PATTERNS.items():
                if re.match(pattern, first_line):
                    return language
        
        # Simple heuristics based on content
        content_lower = content.lower()
        
        if 'def ' in content and 'import ' in content:
            return SupportedLanguage.PYTHON
        elif 'function ' in content and ('var ' in content or 'let ' in content or 'const ' in content):
            return SupportedLanguage.JAVASCRIPT
        elif 'fn ' in content and 'use ' in content:
            return SupportedLanguage.RUST
        elif 'func ' in content and 'package ' in content:
            return SupportedLanguage.GO
        
        return None
    
    def detect(self, path: Path, content: Optional[str] = None) -> Optional[SupportedLanguage]:
        """Detect language from path and optionally content."""
        # Try path-based detection first
        language = self.detect_from_path(path)
        if language:
            return language
        
        # Fall back to content-based detection
        if content:
            return self.detect_from_content(content)
        
        return None


class CodeParser(LoggerMixin):
    """Parses source code files using tree-sitter."""
    
    def __init__(self):
        self.detector = LanguageDetector()
        self._parsers: Dict[SupportedLanguage, Optional[Parser]] = {}
        self._languages: Dict[SupportedLanguage, Optional[Language]] = {}
        self._initialize_parsers()
    
    def _initialize_parsers(self) -> None:
        """Initialize tree-sitter parsers for supported languages."""
        if tree_sitter is None:
            self.logger.warning("tree-sitter not available, parsing will be limited")
            return
        
        # TODO: In a real implementation, you would build and load the language libraries
        # For now, we'll just set up the structure
        for lang in SupportedLanguage:
            self._parsers[lang] = None
            self._languages[lang] = None
        
        self.logger.info("Tree-sitter parsers initialized")
    
    def _get_parser(self, language: SupportedLanguage) -> Optional[Parser]:
        """Get or create a parser for the given language."""
        if tree_sitter is None:
            return None
        
        if language not in self._parsers:
            return None
        
        parser = self._parsers[language]
        if parser is None:
            # TODO: Load actual language library
            # parser = Parser()
            # parser.set_language(self._languages[language])
            # self._parsers[language] = parser
            pass
        
        return parser
    
    def parse_file(self, file_path: Path, max_size_mb: int = 10) -> Optional[ParsedFile]:
        """
        Parse a single source code file.
        
        Args:
            file_path: Path to the file to parse
            max_size_mb: Maximum file size to parse in MB
            
        Returns:
            ParsedFile object or None if parsing failed
        """
        try:
            if not file_path.exists():
                self.logger.warning("File does not exist", path=str(file_path))
                return None
            
            # Check file size
            size_bytes = file_path.stat().st_size
            if size_bytes > max_size_mb * 1024 * 1024:
                self.logger.warning("File too large, skipping", path=str(file_path), size_mb=size_bytes / 1024 / 1024)
                return None
            
            # Read file content
            try:
                content = file_path.read_text(encoding='utf-8')
                encoding = 'utf-8'
            except UnicodeDecodeError:
                try:
                    content = file_path.read_text(encoding='latin-1')
                    encoding = 'latin-1'
                except Exception as e:
                    self.logger.error("Failed to read file", path=str(file_path), error=str(e))
                    return None
            
            # Detect language
            language = self.detector.detect(file_path, content)
            if language is None:
                self.logger.debug("Unknown language, skipping", path=str(file_path))
                return None
            
            # Create parsed file object
            parsed_file = ParsedFile(
                path=file_path,
                language=language,
                content=content,
                encoding=encoding
            )
            
            # Parse with tree-sitter if available
            parser = self._get_parser(language)
            if parser:
                try:
                    ast = parser.parse(content.encode(encoding))
                    parsed_file.ast = ast.root_node
                except Exception as e:
                    self.logger.warning("AST parsing failed", path=str(file_path), error=str(e))
            
            self.logger.debug("File parsed successfully", path=str(file_path), language=language.value)
            return parsed_file
            
        except Exception as e:
            self.logger.error("File parsing failed", path=str(file_path), error=str(e))
            return None
    
    def parse_directory(
        self, 
        directory: Path, 
        recursive: bool = True,
        ignore_patterns: Optional[List[str]] = None
    ) -> List[ParsedFile]:
        """
        Parse all supported files in a directory.
        
        Args:
            directory: Directory to parse
            recursive: Whether to parse subdirectories
            ignore_patterns: Patterns to ignore (glob-style)
            
        Returns:
            List of successfully parsed files
        """
        if ignore_patterns is None:
            ignore_patterns = [
                "*.pyc", "*.pyo", "*.pyd", "__pycache__", ".git", ".svn",
                "node_modules", "target", "build", "dist", ".venv", "venv"
            ]
        
        parsed_files = []
        
        try:
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory.glob(pattern):
                if not file_path.is_file():
                    continue
                
                # Check ignore patterns
                should_ignore = False
                for pattern in ignore_patterns:
                    if file_path.match(pattern) or any(part.startswith('.') for part in file_path.parts[len(directory.parts):]):
                        should_ignore = True
                        break
                
                if should_ignore:
                    continue
                
                parsed_file = self.parse_file(file_path)
                if parsed_file:
                    parsed_files.append(parsed_file)
            
            self.logger.info("Directory parsing completed", 
                           directory=str(directory), 
                           files_parsed=len(parsed_files))
            
        except Exception as e:
            self.logger.error("Directory parsing failed", directory=str(directory), error=str(e))
            raise AnalysisError(f"Failed to parse directory {directory}: {e}")
        
        return parsed_files
    
    def get_supported_languages(self) -> List[SupportedLanguage]:
        """Get list of supported languages."""
        return list(SupportedLanguage)
    
    def get_file_extensions(self) -> Dict[str, SupportedLanguage]:
        """Get mapping of file extensions to languages."""
        return self.detector.EXTENSION_MAP.copy()
