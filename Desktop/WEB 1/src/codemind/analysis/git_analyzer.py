"""
Git repository analysis for CodeMind.

This module analyzes git history to understand development patterns,
code evolution, and team preferences.
"""

import re
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, Counter

try:
    import git
    from git import Repo, Commit, Diff
except ImportError:
    git = None
    Repo = None
    Commit = None
    Diff = None

from ..core.logging import LoggerMixin
from ..core.exceptions import AnalysisError


@dataclass
class CommitAnalysis:
    """Analysis of a single commit."""
    hash: str
    author: str
    date: datetime
    message: str
    files_changed: List[str]
    lines_added: int
    lines_deleted: int
    file_types: Set[str]
    is_merge: bool = False
    is_feature: bool = False
    is_bugfix: bool = False
    is_refactor: bool = False


@dataclass
class DeveloperProfile:
    """Profile of a developer based on git history."""
    name: str
    email: str
    commit_count: int
    lines_added: int
    lines_deleted: int
    favorite_languages: List[str]
    commit_frequency: Dict[str, int]  # day of week -> count
    preferred_file_types: List[str]
    avg_commit_size: float
    last_activity: datetime


@dataclass
class RepositoryInsights:
    """Insights about the repository."""
    total_commits: int
    active_developers: List[DeveloperProfile]
    most_changed_files: List[Tuple[str, int]]
    language_distribution: Dict[str, int]
    commit_patterns: Dict[str, Any]
    development_velocity: Dict[str, float]  # commits per day/week/month
    hotspots: List[str]  # frequently changed files
    
    
class GitAnalyzer(LoggerMixin):
    """Analyzes git repositories for development patterns."""
    
    def __init__(self, repo_path: Optional[Path] = None):
        self.repo_path = repo_path or Path.cwd()
        self.repo = None
        self._initialize_repo()
    
    def _initialize_repo(self) -> None:
        """Initialize git repository connection."""
        if git is None:
            self.logger.warning("GitPython not available, git analysis disabled")
            return
        
        try:
            self.repo = Repo(self.repo_path, search_parent_directories=True)
            self.logger.info("Git repository initialized", path=str(self.repo.working_dir))
        except Exception as e:
            self.logger.warning("Failed to initialize git repository", error=str(e))
            self.repo = None
    
    def is_git_repo(self) -> bool:
        """Check if current directory is a git repository."""
        return self.repo is not None
    
    def analyze_commit(self, commit: Commit) -> CommitAnalysis:
        """Analyze a single commit."""
        try:
            # Get commit stats
            stats = commit.stats
            total_lines_added = stats.total['insertions']
            total_lines_deleted = stats.total['deletions']
            
            # Get changed files
            files_changed = list(stats.files.keys())
            
            # Determine file types
            file_types = set()
            for file_path in files_changed:
                suffix = Path(file_path).suffix.lower()
                if suffix:
                    file_types.add(suffix)
            
            # Analyze commit message
            message = commit.message.strip()
            is_merge = len(commit.parents) > 1
            is_feature = any(keyword in message.lower() for keyword in ['feat', 'feature', 'add', 'implement'])
            is_bugfix = any(keyword in message.lower() for keyword in ['fix', 'bug', 'patch', 'resolve'])
            is_refactor = any(keyword in message.lower() for keyword in ['refactor', 'cleanup', 'improve'])
            
            return CommitAnalysis(
                hash=commit.hexsha[:8],
                author=commit.author.name,
                date=datetime.fromtimestamp(commit.committed_date),
                message=message,
                files_changed=files_changed,
                lines_added=total_lines_added,
                lines_deleted=total_lines_deleted,
                file_types=file_types,
                is_merge=is_merge,
                is_feature=is_feature,
                is_bugfix=is_bugfix,
                is_refactor=is_refactor
            )
            
        except Exception as e:
            self.logger.error("Failed to analyze commit", commit=commit.hexsha, error=str(e))
            raise AnalysisError(f"Commit analysis failed: {e}")
    
    def analyze_recent_commits(self, days: int = 30, max_commits: int = 1000) -> List[CommitAnalysis]:
        """Analyze recent commits."""
        if not self.repo:
            return []
        
        try:
            since_date = datetime.now() - timedelta(days=days)
            commits = list(self.repo.iter_commits(
                since=since_date,
                max_count=max_commits
            ))
            
            analyses = []
            for commit in commits:
                try:
                    analysis = self.analyze_commit(commit)
                    analyses.append(analysis)
                except Exception as e:
                    self.logger.warning("Skipping commit analysis", commit=commit.hexsha, error=str(e))
                    continue
            
            self.logger.info("Recent commits analyzed", 
                           count=len(analyses), 
                           days=days)
            
            return analyses
            
        except Exception as e:
            self.logger.error("Failed to analyze recent commits", error=str(e))
            return []
    
    def build_developer_profiles(self, commit_analyses: List[CommitAnalysis]) -> List[DeveloperProfile]:
        """Build developer profiles from commit analyses."""
        developer_data = defaultdict(lambda: {
            'commits': [],
            'lines_added': 0,
            'lines_deleted': 0,
            'file_types': Counter(),
            'commit_frequency': Counter(),
        })
        
        # Aggregate data by developer
        for analysis in commit_analyses:
            dev_data = developer_data[analysis.author]
            dev_data['commits'].append(analysis)
            dev_data['lines_added'] += analysis.lines_added
            dev_data['lines_deleted'] += analysis.lines_deleted
            
            # Track file types
            for file_type in analysis.file_types:
                dev_data['file_types'][file_type] += 1
            
            # Track commit frequency by day of week
            day_of_week = analysis.date.strftime('%A')
            dev_data['commit_frequency'][day_of_week] += 1
        
        # Build profiles
        profiles = []
        for author, data in developer_data.items():
            commits = data['commits']
            if not commits:
                continue
            
            # Calculate average commit size
            total_changes = sum(c.lines_added + c.lines_deleted for c in commits)
            avg_commit_size = total_changes / len(commits) if commits else 0
            
            # Get favorite languages (top file types)
            favorite_languages = [ext for ext, _ in data['file_types'].most_common(3)]
            preferred_file_types = [ext for ext, _ in data['file_types'].most_common(5)]
            
            profile = DeveloperProfile(
                name=author,
                email="",  # Would need to extract from git config
                commit_count=len(commits),
                lines_added=data['lines_added'],
                lines_deleted=data['lines_deleted'],
                favorite_languages=favorite_languages,
                commit_frequency=dict(data['commit_frequency']),
                preferred_file_types=preferred_file_types,
                avg_commit_size=avg_commit_size,
                last_activity=max(c.date for c in commits)
            )
            profiles.append(profile)
        
        return sorted(profiles, key=lambda p: p.commit_count, reverse=True)
    
    def identify_hotspots(self, commit_analyses: List[CommitAnalysis], min_changes: int = 3) -> List[str]:
        """Identify frequently changed files (hotspots)."""
        file_change_count = Counter()
        
        for analysis in commit_analyses:
            for file_path in analysis.files_changed:
                file_change_count[file_path] += 1
        
        hotspots = [
            file_path for file_path, count in file_change_count.most_common()
            if count >= min_changes
        ]
        
        return hotspots
    
    def calculate_development_velocity(self, commit_analyses: List[CommitAnalysis]) -> Dict[str, float]:
        """Calculate development velocity metrics."""
        if not commit_analyses:
            return {}
        
        # Sort by date
        sorted_commits = sorted(commit_analyses, key=lambda c: c.date)
        
        if len(sorted_commits) < 2:
            return {}
        
        # Calculate time span
        start_date = sorted_commits[0].date
        end_date = sorted_commits[-1].date
        total_days = (end_date - start_date).days
        
        if total_days == 0:
            return {}
        
        # Calculate velocities
        total_commits = len(commit_analyses)
        commits_per_day = total_commits / total_days
        commits_per_week = commits_per_day * 7
        commits_per_month = commits_per_day * 30
        
        # Calculate code velocity
        total_lines_changed = sum(c.lines_added + c.lines_deleted for c in commit_analyses)
        lines_per_day = total_lines_changed / total_days
        
        return {
            'commits_per_day': commits_per_day,
            'commits_per_week': commits_per_week,
            'commits_per_month': commits_per_month,
            'lines_per_day': lines_per_day,
            'avg_commit_size': total_lines_changed / total_commits,
        }
    
    def analyze_repository(self, days: int = 90) -> Optional[RepositoryInsights]:
        """Perform comprehensive repository analysis."""
        if not self.repo:
            self.logger.warning("No git repository available for analysis")
            return None
        
        try:
            # Analyze recent commits
            commit_analyses = self.analyze_recent_commits(days=days)
            
            if not commit_analyses:
                self.logger.warning("No commits found for analysis")
                return None
            
            # Build developer profiles
            developer_profiles = self.build_developer_profiles(commit_analyses)
            
            # Identify hotspots
            hotspots = self.identify_hotspots(commit_analyses)
            
            # Calculate velocity
            velocity = self.calculate_development_velocity(commit_analyses)
            
            # Analyze language distribution
            language_dist = Counter()
            for analysis in commit_analyses:
                for file_type in analysis.file_types:
                    language_dist[file_type] += 1
            
            # Analyze commit patterns
            commit_patterns = {
                'feature_commits': len([c for c in commit_analyses if c.is_feature]),
                'bugfix_commits': len([c for c in commit_analyses if c.is_bugfix]),
                'refactor_commits': len([c for c in commit_analyses if c.is_refactor]),
                'merge_commits': len([c for c in commit_analyses if c.is_merge]),
            }
            
            # Most changed files
            file_changes = Counter()
            for analysis in commit_analyses:
                for file_path in analysis.files_changed:
                    file_changes[file_path] += 1
            
            insights = RepositoryInsights(
                total_commits=len(commit_analyses),
                active_developers=developer_profiles,
                most_changed_files=file_changes.most_common(10),
                language_distribution=dict(language_dist),
                commit_patterns=commit_patterns,
                development_velocity=velocity,
                hotspots=hotspots[:20]  # Top 20 hotspots
            )
            
            self.logger.info("Repository analysis completed",
                           commits_analyzed=len(commit_analyses),
                           developers=len(developer_profiles),
                           hotspots=len(hotspots))
            
            return insights
            
        except Exception as e:
            self.logger.error("Repository analysis failed", error=str(e))
            raise AnalysisError(f"Repository analysis failed: {e}")
    
    def get_file_history(self, file_path: str, max_commits: int = 50) -> List[CommitAnalysis]:
        """Get commit history for a specific file."""
        if not self.repo:
            return []
        
        try:
            commits = list(self.repo.iter_commits(
                paths=file_path,
                max_count=max_commits
            ))
            
            analyses = []
            for commit in commits:
                try:
                    analysis = self.analyze_commit(commit)
                    analyses.append(analysis)
                except Exception as e:
                    self.logger.warning("Skipping commit in file history", 
                                      commit=commit.hexsha, 
                                      file=file_path, 
                                      error=str(e))
                    continue
            
            return analyses
            
        except Exception as e:
            self.logger.error("Failed to get file history", file=file_path, error=str(e))
            return []
