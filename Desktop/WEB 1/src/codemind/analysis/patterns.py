"""
Pattern extraction and analysis for CodeMind.

This module identifies common coding patterns, architectural decisions,
and style preferences from parsed code files.
"""

import re
import ast
from pathlib import Path
from typing import Dict, List, Optional, Set, Any, Tuple, Union
from dataclasses import dataclass, field
from collections import defaultdict, Counter
from enum import Enum

from .parser import ParsedFile, SupportedLanguage
from ..core.logging import LoggerMixin


class PatternType(Enum):
    """Types of code patterns that can be extracted."""
    FUNCTION_SIGNATURE = "function_signature"
    CLASS_STRUCTURE = "class_structure"
    IMPORT_STYLE = "import_style"
    NAMING_CONVENTION = "naming_convention"
    ARCHITECTURAL = "architectural"
    ERROR_HANDLING = "error_handling"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    CONFIGURATION = "configuration"


@dataclass
class CodePattern:
    """Represents a discovered code pattern."""
    type: PatternType
    language: SupportedLanguage
    pattern: str
    description: str
    examples: List[str] = field(default_factory=list)
    frequency: int = 1
    confidence: float = 1.0
    file_paths: Set[Path] = field(default_factory=set)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_example(self, example: str, file_path: Path) -> None:
        """Add an example of this pattern."""
        if example not in self.examples:
            self.examples.append(example)
        self.file_paths.add(file_path)
        self.frequency += 1
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert pattern to dictionary for serialization."""
        return {
            "type": self.type.value,
            "language": self.language.value,
            "pattern": self.pattern,
            "description": self.description,
            "examples": self.examples[:5],  # Limit examples
            "frequency": self.frequency,
            "confidence": self.confidence,
            "file_count": len(self.file_paths),
            "metadata": self.metadata
        }


class PythonPatternExtractor(LoggerMixin):
    """Extracts patterns from Python code."""
    
    def extract_patterns(self, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract patterns from a Python file."""
        patterns = []
        
        try:
            # Parse Python AST
            tree = ast.parse(parsed_file.content)
            
            # Extract import patterns
            patterns.extend(self._extract_import_patterns(tree, parsed_file))
            
            # Extract function patterns
            patterns.extend(self._extract_function_patterns(tree, parsed_file))
            
            # Extract class patterns
            patterns.extend(self._extract_class_patterns(tree, parsed_file))
            
            # Extract naming conventions
            patterns.extend(self._extract_naming_patterns(tree, parsed_file))
            
            # Extract error handling patterns
            patterns.extend(self._extract_error_handling_patterns(tree, parsed_file))
            
        except SyntaxError as e:
            self.logger.warning("Python syntax error", file=str(parsed_file.path), error=str(e))
        except Exception as e:
            self.logger.error("Pattern extraction failed", file=str(parsed_file.path), error=str(e))
        
        return patterns
    
    def _extract_import_patterns(self, tree: ast.AST, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract import style patterns."""
        patterns = []
        imports = []
        from_imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    from_imports.append(f"from {module} import {alias.name}")
        
        if imports:
            pattern = CodePattern(
                type=PatternType.IMPORT_STYLE,
                language=SupportedLanguage.PYTHON,
                pattern="direct_imports",
                description="Uses direct import statements",
                examples=imports[:3]
            )
            pattern.file_paths.add(parsed_file.path)
            patterns.append(pattern)
        
        if from_imports:
            pattern = CodePattern(
                type=PatternType.IMPORT_STYLE,
                language=SupportedLanguage.PYTHON,
                pattern="from_imports",
                description="Uses from...import statements",
                examples=from_imports[:3]
            )
            pattern.file_paths.add(parsed_file.path)
            patterns.append(pattern)
        
        return patterns
    
    def _extract_function_patterns(self, tree: ast.AST, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract function signature patterns."""
        patterns = []
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Analyze function signature
                args = [arg.arg for arg in node.args.args]
                has_defaults = len(node.args.defaults) > 0
                has_kwargs = node.args.kwarg is not None
                has_varargs = node.args.vararg is not None
                has_annotations = any(arg.annotation for arg in node.args.args)
                
                signature_info = {
                    "name": node.name,
                    "arg_count": len(args),
                    "has_defaults": has_defaults,
                    "has_kwargs": has_kwargs,
                    "has_varargs": has_varargs,
                    "has_annotations": has_annotations
                }
                functions.append(signature_info)
        
        if functions:
            # Analyze patterns
            avg_args = sum(f["arg_count"] for f in functions) / len(functions)
            uses_annotations = sum(f["has_annotations"] for f in functions) / len(functions)
            
            if uses_annotations > 0.5:
                pattern = CodePattern(
                    type=PatternType.FUNCTION_SIGNATURE,
                    language=SupportedLanguage.PYTHON,
                    pattern="type_annotations",
                    description="Frequently uses type annotations",
                    confidence=uses_annotations
                )
                pattern.file_paths.add(parsed_file.path)
                patterns.append(pattern)
        
        return patterns
    
    def _extract_class_patterns(self, tree: ast.AST, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract class structure patterns."""
        patterns = []
        classes = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                methods = [n for n in node.body if isinstance(n, ast.FunctionDef)]
                has_init = any(m.name == "__init__" for m in methods)
                has_str = any(m.name == "__str__" for m in methods)
                has_repr = any(m.name == "__repr__" for m in methods)
                
                class_info = {
                    "name": node.name,
                    "method_count": len(methods),
                    "has_init": has_init,
                    "has_str": has_str,
                    "has_repr": has_repr,
                    "base_classes": len(node.bases)
                }
                classes.append(class_info)
        
        if classes:
            uses_dunder_methods = sum(c["has_str"] or c["has_repr"] for c in classes) / len(classes)
            
            if uses_dunder_methods > 0.3:
                pattern = CodePattern(
                    type=PatternType.CLASS_STRUCTURE,
                    language=SupportedLanguage.PYTHON,
                    pattern="dunder_methods",
                    description="Implements dunder methods (__str__, __repr__)",
                    confidence=uses_dunder_methods
                )
                pattern.file_paths.add(parsed_file.path)
                patterns.append(pattern)
        
        return patterns
    
    def _extract_naming_patterns(self, tree: ast.AST, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract naming convention patterns."""
        patterns = []
        
        function_names = []
        class_names = []
        variable_names = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                function_names.append(node.name)
            elif isinstance(node, ast.ClassDef):
                class_names.append(node.name)
            elif isinstance(node, ast.Name) and isinstance(node.ctx, ast.Store):
                variable_names.append(node.id)
        
        # Analyze naming patterns
        if function_names:
            snake_case_funcs = sum(1 for name in function_names if '_' in name and name.islower())
            if snake_case_funcs / len(function_names) > 0.8:
                pattern = CodePattern(
                    type=PatternType.NAMING_CONVENTION,
                    language=SupportedLanguage.PYTHON,
                    pattern="snake_case_functions",
                    description="Uses snake_case for function names",
                    confidence=snake_case_funcs / len(function_names)
                )
                pattern.file_paths.add(parsed_file.path)
                patterns.append(pattern)
        
        if class_names:
            pascal_case_classes = sum(1 for name in class_names if name[0].isupper() and '_' not in name)
            if pascal_case_classes / len(class_names) > 0.8:
                pattern = CodePattern(
                    type=PatternType.NAMING_CONVENTION,
                    language=SupportedLanguage.PYTHON,
                    pattern="pascal_case_classes",
                    description="Uses PascalCase for class names",
                    confidence=pascal_case_classes / len(class_names)
                )
                pattern.file_paths.add(parsed_file.path)
                patterns.append(pattern)
        
        return patterns
    
    def _extract_error_handling_patterns(self, tree: ast.AST, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract error handling patterns."""
        patterns = []
        
        try_blocks = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Try):
                exception_types = []
                for handler in node.handlers:
                    if handler.type:
                        if isinstance(handler.type, ast.Name):
                            exception_types.append(handler.type.id)
                        elif isinstance(handler.type, ast.Tuple):
                            for elt in handler.type.elts:
                                if isinstance(elt, ast.Name):
                                    exception_types.append(elt.id)
                
                try_blocks.append({
                    "exception_types": exception_types,
                    "has_finally": len(node.finalbody) > 0,
                    "has_else": len(node.orelse) > 0
                })
        
        if try_blocks:
            pattern = CodePattern(
                type=PatternType.ERROR_HANDLING,
                language=SupportedLanguage.PYTHON,
                pattern="try_except_blocks",
                description="Uses try/except for error handling",
                frequency=len(try_blocks)
            )
            pattern.file_paths.add(parsed_file.path)
            patterns.append(pattern)
        
        return patterns


class PatternExtractor(LoggerMixin):
    """Main pattern extraction coordinator."""
    
    def __init__(self):
        self.extractors = {
            SupportedLanguage.PYTHON: PythonPatternExtractor()
        }
    
    def extract_from_file(self, parsed_file: ParsedFile) -> List[CodePattern]:
        """Extract patterns from a single parsed file."""
        extractor = self.extractors.get(parsed_file.language)
        if not extractor:
            self.logger.debug("No pattern extractor for language", 
                            language=parsed_file.language.value,
                            file=str(parsed_file.path))
            return []
        
        return extractor.extract_patterns(parsed_file)
    
    def extract_from_files(self, parsed_files: List[ParsedFile]) -> List[CodePattern]:
        """Extract and consolidate patterns from multiple files."""
        all_patterns = []
        pattern_map = defaultdict(list)
        
        # Extract patterns from each file
        for parsed_file in parsed_files:
            file_patterns = self.extract_from_file(parsed_file)
            all_patterns.extend(file_patterns)
            
            # Group similar patterns
            for pattern in file_patterns:
                key = (pattern.type, pattern.language, pattern.pattern)
                pattern_map[key].append(pattern)
        
        # Consolidate similar patterns
        consolidated_patterns = []
        for key, patterns in pattern_map.items():
            if len(patterns) == 1:
                consolidated_patterns.append(patterns[0])
            else:
                # Merge patterns
                base_pattern = patterns[0]
                for pattern in patterns[1:]:
                    base_pattern.frequency += pattern.frequency
                    base_pattern.examples.extend(pattern.examples)
                    base_pattern.file_paths.update(pattern.file_paths)
                
                # Update confidence based on frequency
                base_pattern.confidence = min(1.0, base_pattern.frequency / len(parsed_files))
                consolidated_patterns.append(base_pattern)
        
        self.logger.info("Pattern extraction completed",
                        files_processed=len(parsed_files),
                        patterns_found=len(consolidated_patterns))
        
        return consolidated_patterns
    
    def get_pattern_summary(self, patterns: List[CodePattern]) -> Dict[str, Any]:
        """Generate a summary of extracted patterns."""
        summary = {
            "total_patterns": len(patterns),
            "by_type": Counter(p.type.value for p in patterns),
            "by_language": Counter(p.language.value for p in patterns),
            "high_confidence": len([p for p in patterns if p.confidence > 0.8]),
            "most_frequent": sorted(patterns, key=lambda p: p.frequency, reverse=True)[:5]
        }
        
        return summary
