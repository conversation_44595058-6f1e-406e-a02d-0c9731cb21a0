Metadata-Version: 2.4
Name: codemind
Version: 0.1.0
Summary: The Self-Evolving Code Assistant - An autonomous CLI tool that learns from codebases
Author-email: CodeMind Team <<EMAIL>>
Maintainer-email: CodeMind Team <<EMAIL>>
License: AGPL-3.0
Project-URL: Homepage, https://github.com/codemind-ai/codemind
Project-URL: Documentation, https://docs.codemind.dev
Project-URL: Repository, https://github.com/codemind-ai/codemind
Project-URL: Bug Tracker, https://github.com/codemind-ai/codemind/issues
Project-URL: Changelog, https://github.com/codemind-ai/codemind/blob/main/CHANGELOG.md
Keywords: ai,code-assistant,machine-learning,cli,autonomous
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Affero General Public License v3
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: typer[all]>=0.9.0
Requires-Dist: rich>=13.0.0
Requires-Dist: click>=8.0.0
Requires-Dist: torch>=2.0.0
Requires-Dist: transformers>=4.30.0
Requires-Dist: accelerate>=0.20.0
Requires-Dist: bitsandbytes>=0.39.0
Requires-Dist: peft>=0.4.0
Requires-Dist: tree-sitter>=0.20.0
Requires-Dist: tree-sitter-python>=0.20.0
Requires-Dist: tree-sitter-javascript>=0.20.0
Requires-Dist: tree-sitter-typescript>=0.20.0
Requires-Dist: tree-sitter-rust>=0.20.0
Requires-Dist: tree-sitter-go>=0.20.0
Requires-Dist: semgrep>=1.30.0
Requires-Dist: chromadb>=0.4.0
Requires-Dist: sqlite-utils>=3.30.0
Requires-Dist: sqlalchemy>=2.0.0
Requires-Dist: pynacl>=1.5.0
Requires-Dist: cryptography>=41.0.0
Requires-Dist: keyring>=24.0.0
Requires-Dist: gitpython>=3.1.0
Requires-Dist: dulwich>=0.21.0
Requires-Dist: pydantic>=2.0.0
Requires-Dist: httpx>=0.24.0
Requires-Dist: aiofiles>=23.0.0
Requires-Dist: toml>=0.10.0
Requires-Dist: pyyaml>=6.0.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: psutil>=5.9.0
Requires-Dist: structlog>=23.0.0
Requires-Dist: prometheus-client>=0.17.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: flake8>=6.0.0; extra == "dev"
Requires-Dist: mypy>=1.4.0; extra == "dev"
Requires-Dist: pre-commit>=3.3.0; extra == "dev"
Provides-Extra: gpu
Requires-Dist: torch[cuda]>=2.0.0; extra == "gpu"
Requires-Dist: nvidia-ml-py>=12.0.0; extra == "gpu"
Provides-Extra: enterprise
Requires-Dist: redis>=4.5.0; extra == "enterprise"
Requires-Dist: celery>=5.3.0; extra == "enterprise"
Requires-Dist: kubernetes>=27.0.0; extra == "enterprise"

# CodeMind - The Self-Evolving Code Assistant

[![License: AGPL v3](https://img.shields.io/badge/License-AGPL_v3-blue.svg)](https://www.gnu.org/licenses/agpl-3.0)
[![Python 3.10+](https://img.shields.io/badge/Python-3.10%2B-blue.svg)](https://www.python.org/downloads/)
[![CodeStyle: Black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

**An autonomous CLI tool that learns from codebases it interacts with, improving its capabilities through continuous self-modification while maintaining operational integrity.**

## 🌟 Vision

Create an open-source alternative to commercial code assistants that:
- Improves through real-world usage patterns
- Maintains complete data privacy
- Self-modifies using secure, verifiable update mechanisms
- Specializes in user/team-specific coding patterns

## 🚀 Quick Start

### Installation

```bash
# Install from PyPI (coming soon)
pip install codemind

# Or install from source
git clone https://github.com/codemind-ai/codemind.git
cd codemind
pip install -e .
```

### Basic Usage

```bash
# Initialize CodeMind in your project
codemind init

# Analyze your codebase
codemind analyze

# Get code suggestions
codemind suggest --feature "authentication system"

# Learn from your changes
codemind learn --from-git

# Check system status
codemind status
```

## 🏗️ Architecture

CodeMind uses a distributed "spider web" learning system where:

1. **Local Node**: Runs on your machine with complete privacy
2. **Pattern Recognition**: Learns from your codebase and git history
3. **Federated Learning**: Optionally shares anonymized patterns with the network
4. **Self-Evolution**: Updates its own models based on successful patterns

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Your Machine  │    │  Spider Network │    │  Other Nodes    │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ Local Model │◄┼────┼►│Master Model │◄┼────┼►│ Local Model │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │  Your Code  │ │    │ │  Patterns   │ │    │  Other Code   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔒 Privacy & Security

- **Local-First**: Your code never leaves your machine unless you opt-in
- **Encrypted Updates**: All model updates are cryptographically signed
- **Differential Privacy**: Shared patterns are anonymized and aggregated
- **Audit Trail**: Complete transparency of what data is shared

## 📊 Current Status

| Feature                  | Status  | Version |
|--------------------------|---------|---------|
| CLI Framework            | ✅ Done | v0.1.0  |
| Local Model Inference    | 🚧 WIP  | v0.1.0  |
| Codebase Analysis        | 🚧 WIP  | v0.1.0  |
| Pattern Recognition      | 📋 TODO | v0.2.0  |
| Self-Modification        | 📋 TODO | v0.3.0  |
| Federated Learning       | 📋 TODO | v0.4.0  |

## 🛠️ Development

### Prerequisites

- Python 3.10+
- Git
- 8GB+ RAM (for local models)
- Optional: CUDA-compatible GPU

### Setup Development Environment

```bash
# Clone the repository
git clone https://github.com/codemind-ai/codemind.git
cd codemind

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest
```

### Project Structure

```
codemind/
├── src/codemind/           # Main package
│   ├── cli/               # CLI interface
│   ├── core/              # Core functionality
│   ├── ml/                # Machine learning components
│   ├── analysis/          # Code analysis engine
│   ├── security/          # Security and crypto
│   ├── storage/           # Knowledge storage
│   └── spider/            # Distributed learning
├── tests/                 # Test suite
├── docs/                  # Documentation
├── scripts/               # Utility scripts
└── examples/              # Example configurations
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Key Areas for Contribution

1. **Language Support**: Add tree-sitter parsers for new languages
2. **Model Optimization**: Improve inference speed and accuracy
3. **Security Auditing**: Review cryptographic implementations
4. **Documentation**: Improve user guides and API docs

## 📄 License

This project is licensed under the GNU Affero General Public License v3.0 - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Hugging Face](https://huggingface.co/) for transformer models
- [Tree-sitter](https://tree-sitter.github.io/) for code parsing
- [ChromaDB](https://www.trychroma.com/) for vector storage
- The open-source community for inspiration and tools

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/codemind)
- 🐛 Issues: [GitHub Issues](https://github.com/codemind-ai/codemind/issues)
- 📖 Docs: [docs.codemind.dev](https://docs.codemind.dev)

---

**Made with ❤️ by the CodeMind team**
