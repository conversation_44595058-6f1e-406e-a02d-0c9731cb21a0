#!/usr/bin/env python3
"""
Build tree-sitter language parsers for CodeMind.

This script downloads and builds the actual tree-sitter language
parsers needed for real code analysis.
"""

import os
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List

# Language repositories
LANGUAGE_REPOS = {
    "python": "https://github.com/tree-sitter/tree-sitter-python",
    "javascript": "https://github.com/tree-sitter/tree-sitter-javascript", 
    "typescript": "https://github.com/tree-sitter/tree-sitter-typescript",
    "rust": "https://github.com/tree-sitter/tree-sitter-rust",
    "go": "https://github.com/tree-sitter/tree-sitter-go",
    "java": "https://github.com/tree-sitter/tree-sitter-java",
    "cpp": "https://github.com/tree-sitter/tree-sitter-cpp",
    "c": "https://github.com/tree-sitter/tree-sitter-c",
}


def build_language_parser(language: str, repo_url: str, build_dir: Path) -> bool:
    """Build a tree-sitter language parser."""
    print(f"🔧 Building {language} parser...")
    
    lang_dir = build_dir / f"tree-sitter-{language}"
    
    try:
        # Clone repository
        if not lang_dir.exists():
            subprocess.run([
                "git", "clone", repo_url, str(lang_dir)
            ], check=True)
        
        # Build parser
        os.chdir(lang_dir)
        
        # Create build script
        build_script = f"""
from tree_sitter import Language, Parser

Language.build_library(
    'build/{language}.so',
    ['.']
)
"""
        
        with open("build_parser.py", "w") as f:
            f.write(build_script)
        
        # Run build
        subprocess.run(["python", "build_parser.py"], check=True)
        
        print(f"✅ {language} parser built successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to build {language} parser: {e}")
        return False


def main():
    """Build all language parsers."""
    print("🏗️  Building tree-sitter language parsers...")
    
    build_dir = Path("build/parsers")
    build_dir.mkdir(parents=True, exist_ok=True)
    
    original_dir = Path.cwd()
    
    try:
        for language, repo_url in LANGUAGE_REPOS.items():
            success = build_language_parser(language, repo_url, build_dir)
            os.chdir(original_dir)
            
            if not success:
                print(f"⚠️  Continuing without {language} parser")
        
        print("🎉 Parser building completed!")
        
    except KeyboardInterrupt:
        print("\n👋 Build interrupted")
    finally:
        os.chdir(original_dir)


if __name__ == "__main__":
    main()
