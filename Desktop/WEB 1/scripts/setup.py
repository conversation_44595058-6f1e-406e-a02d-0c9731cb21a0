#!/usr/bin/env python3
"""
Setup script for CodeMind development environment.

This script helps set up the development environment for CodeMind,
including virtual environment creation, dependency installation,
and basic configuration.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def run_command(cmd, check=True, shell=False):
    """Run a command and return the result."""
    print(f"🔧 Running: {cmd}")
    try:
        if isinstance(cmd, str) and not shell:
            cmd = cmd.split()
        
        result = subprocess.run(
            cmd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=shell
        )
        
        if result.stdout:
            print(result.stdout)
        
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_python_version():
    """Check if Python version is compatible."""
    print("🐍 Checking Python version...")
    
    version = sys.version_info
    if version.major != 3 or version.minor < 10:
        print(f"❌ Python 3.10+ required, found {version.major}.{version.minor}")
        sys.exit(1)
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")


def create_virtual_environment():
    """Create a virtual environment."""
    print("📦 Creating virtual environment...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("⚠️  Virtual environment already exists")
        return
    
    run_command([sys.executable, "-m", "venv", "venv"])
    print("✅ Virtual environment created")


def get_pip_command():
    """Get the pip command for the current platform."""
    if platform.system() == "Windows":
        return "venv\\Scripts\\pip"
    else:
        return "venv/bin/pip"


def get_python_command():
    """Get the python command for the current platform."""
    if platform.system() == "Windows":
        return "venv\\Scripts\\python"
    else:
        return "venv/bin/python"


def install_dependencies():
    """Install project dependencies."""
    print("📚 Installing dependencies...")
    
    pip_cmd = get_pip_command()
    
    # Upgrade pip first
    run_command([pip_cmd, "install", "--upgrade", "pip"])
    
    # Install main dependencies
    run_command([pip_cmd, "install", "-r", "requirements.txt"])
    
    # Install development dependencies
    run_command([pip_cmd, "install", "-e", ".[dev]"])
    
    print("✅ Dependencies installed")


def setup_pre_commit():
    """Set up pre-commit hooks."""
    print("🔗 Setting up pre-commit hooks...")
    
    python_cmd = get_python_command()
    
    try:
        run_command([python_cmd, "-m", "pre_commit", "install"])
        print("✅ Pre-commit hooks installed")
    except:
        print("⚠️  Pre-commit setup failed (optional)")


def create_config_directories():
    """Create necessary configuration directories."""
    print("📁 Creating configuration directories...")
    
    dirs = [
        Path.home() / ".codemind",
        Path.home() / ".codemind" / "models",
        Path.home() / ".codemind" / "cache",
        Path.home() / ".codemind" / "logs",
    ]
    
    for dir_path in dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
    
    print("✅ Configuration directories created")


def check_optional_dependencies():
    """Check for optional dependencies."""
    print("🔍 Checking optional dependencies...")
    
    optional_deps = {
        "git": "Git (for repository analysis)",
        "docker": "Docker (for containerized deployment)",
        "nvidia-smi": "NVIDIA GPU support",
    }
    
    for cmd, description in optional_deps.items():
        try:
            run_command([cmd, "--version"], check=False)
            print(f"✅ {description}: Available")
        except:
            print(f"⚠️  {description}: Not available")


def run_basic_tests():
    """Run basic tests to verify installation."""
    print("🧪 Running basic tests...")
    
    python_cmd = get_python_command()
    
    try:
        # Test imports
        test_script = """
import codemind
from codemind.core.config import Config
from codemind.analysis.parser import CodeParser
print("✅ All imports successful")
print(f"CodeMind version: {codemind.__version__}")
"""
        
        result = run_command([
            python_cmd, "-c", test_script
        ], check=False)
        
        if result.returncode == 0:
            print("✅ Basic tests passed")
        else:
            print("❌ Basic tests failed")
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")


def main():
    """Main setup function."""
    print("🧠 CodeMind Development Setup")
    print("=" * 40)
    
    # Check prerequisites
    check_python_version()
    
    # Setup steps
    create_virtual_environment()
    install_dependencies()
    setup_pre_commit()
    create_config_directories()
    check_optional_dependencies()
    run_basic_tests()
    
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    
    if platform.system() == "Windows":
        print("  1. Activate virtual environment: venv\\Scripts\\activate")
    else:
        print("  1. Activate virtual environment: source venv/bin/activate")
    
    print("  2. Run tests: pytest")
    print("  3. Try the CLI: python -m codemind.cli --help")
    print("  4. Run example: python examples/basic_usage.py")
    print("  5. Initialize in a project: codemind init")
    
    print("\n📖 Documentation:")
    print("  • README.md - Project overview")
    print("  • docs/ - Detailed documentation")
    print("  • examples/ - Usage examples")
    
    print("\n🤝 Contributing:")
    print("  • Run 'pre-commit run --all-files' before committing")
    print("  • Follow the coding standards in .pre-commit-config.yaml")
    print("  • Add tests for new features")


if __name__ == "__main__":
    main()
