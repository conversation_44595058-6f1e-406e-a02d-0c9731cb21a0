# Getting Started with CodeMind

Welcome to CodeMind - The Self-Evolving Code Assistant! This guide will help you get up and running quickly.

## 🚀 Quick Start

### 1. Prerequisites

- **Python 3.10+** (required)
- **Git** (recommended for repository analysis)
- **8GB+ RAM** (for local model inference)
- **CUDA GPU** (optional, for faster inference)

### 2. Installation

```bash
# Clone the repository
git clone https://github.com/codemind-ai/codemind.git
cd codemind

# Run the setup script
python scripts/setup.py

# Activate the virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. First Run

```bash
# See the demo
python demo.py

# Initialize CodeMind in your project
codemind init

# Analyze your codebase
codemind analyze

# Get your first AI suggestion
codemind suggest "authentication system"
```

## 🧠 Core Concepts

### Local-First AI
- Your code never leaves your machine unless you opt-in
- Models run locally for complete privacy
- No internet required for basic functionality

### Pattern Learning
- Analyzes your codebase to understand your style
- Learns from git history and code changes
- Adapts suggestions to your team's preferences

### Spider Web Network
- Optional federated learning system
- Share anonymized patterns with the community
- Receive improvements from the global knowledge base
- Maintain complete control over your data

## 📋 Basic Commands

### Initialization
```bash
# Initialize in current directory
codemind init

# Initialize with custom settings
codemind init --interactive
```

### Analysis
```bash
# Analyze current directory
codemind analyze

# Analyze specific path
codemind analyze /path/to/code

# Analyze only Python files
codemind analyze --lang python
```

### Code Generation
```bash
# Get suggestions for a feature
codemind suggest "user authentication"

# Get suggestions with context
codemind suggest "database migration" --context "Django project"

# Focus on specific file
codemind suggest "error handling" --file src/main.py
```

### Learning
```bash
# Learn from recent git commits
codemind learn --from-git

# Learn from specific files
codemind learn --file src/new_feature.py
```

### Status & Info
```bash
# Check system status
codemind status

# Show version
codemind version
```

## 🔧 Configuration

CodeMind stores configuration in `~/.codemind/config.yaml`. Key settings:

```yaml
# Model settings
model:
  name: "microsoft/CodeGPT-small-py"
  temperature: 0.1
  device: "auto"

# Security settings
security:
  enable_encryption: true
  audit_logging: true

# Spider web settings
spider:
  enable_federated_learning: false
  privacy_level: "high"

# Analysis settings
analysis:
  supported_languages: ["python", "javascript", "typescript"]
  enable_security_scanning: true
```

## 🎯 Usage Examples

### Example 1: Analyze a Python Project
```bash
cd my-python-project
codemind init
codemind analyze
codemind suggest "add logging to functions"
```

### Example 2: Learn from Git History
```bash
codemind learn --from-git
codemind suggest "refactor this class" --file src/models.py
```

### Example 3: Team Collaboration
```bash
# Enable federated learning (optional)
codemind config set spider.enable_federated_learning true

# Share patterns with team
codemind learn --from-git
```

## 🔒 Privacy & Security

### Data Privacy
- **Local Processing**: All analysis happens on your machine
- **Opt-in Sharing**: Federated learning is disabled by default
- **Anonymization**: Shared data is anonymized and aggregated
- **No Code Sharing**: Only patterns are shared, never actual code

### Security Features
- **Encryption**: All local data is encrypted at rest
- **Audit Logs**: Complete audit trail of all operations
- **Secure Updates**: Model updates are cryptographically signed
- **Sandboxed Execution**: Safe execution environment for model updates

## 🛠️ Development

### Running Tests
```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m slow
```

### Code Quality
```bash
# Format code
black src/ tests/

# Check types
mypy src/

# Lint code
flake8 src/ tests/

# Run all checks
pre-commit run --all-files
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## 🐛 Troubleshooting

### Common Issues

**"No module named 'codemind'"**
```bash
# Make sure you're in the virtual environment
source venv/bin/activate
pip install -e .
```

**"Model loading failed"**
```bash
# Check available memory
codemind status

# Try a smaller model
codemind config set model.name "microsoft/CodeGPT-small-py"
```

**"Git analysis failed"**
```bash
# Make sure you're in a git repository
git init
git add .
git commit -m "Initial commit"
```

### Getting Help

- 📖 **Documentation**: Check the `docs/` directory
- 🐛 **Issues**: Report bugs on GitHub Issues
- 💬 **Discussions**: Join GitHub Discussions
- 📧 **Email**: <EMAIL>

## 🎉 What's Next?

1. **Explore Examples**: Check out `examples/` for more usage patterns
2. **Read Documentation**: Dive deeper with the full docs
3. **Join Community**: Connect with other CodeMind users
4. **Contribute**: Help improve CodeMind for everyone

## 📚 Additional Resources

- [Architecture Overview](docs/architecture.md)
- [API Reference](docs/api.md)
- [Model Guide](docs/models.md)
- [Security Guide](docs/security.md)
- [Contributing Guide](CONTRIBUTING.md)

---

**Happy Coding with CodeMind! 🧠✨**
