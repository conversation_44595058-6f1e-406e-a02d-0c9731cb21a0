#!/usr/bin/env python3
"""
Basic usage examples for CodeMind.

This script demonstrates how to use CodeMind programmatically
for code analysis and generation.
"""

import asyncio
from pathlib import Path

from codemind.core.config import Config
from codemind.analysis.parser import CodeParser
from codemind.analysis.patterns import PatternExtractor
from codemind.ml.inference import CodeGenerator, GenerationRequest
from codemind.ml.models import SupportedModel


async def analyze_codebase_example():
    """Example: Analyze a codebase for patterns."""
    print("🔍 Analyzing codebase...")
    
    # Initialize components
    parser = CodeParser()
    extractor = PatternExtractor()
    
    # Parse current directory
    current_dir = Path.cwd()
    parsed_files = parser.parse_directory(current_dir, recursive=True)
    
    print(f"📁 Found {len(parsed_files)} source files")
    
    # Extract patterns
    patterns = extractor.extract_from_files(parsed_files)
    
    print(f"🎯 Extracted {len(patterns)} patterns")
    
    # Show pattern summary
    summary = extractor.get_pattern_summary(patterns)
    print("\n📊 Pattern Summary:")
    print(f"  • Total patterns: {summary['total_patterns']}")
    print(f"  • High confidence: {summary['high_confidence']}")
    
    print("\n🏷️  By Type:")
    for pattern_type, count in summary['by_type'].items():
        print(f"  • {pattern_type}: {count}")
    
    print("\n🔥 Most Frequent Patterns:")
    for i, pattern in enumerate(summary['most_frequent'][:3], 1):
        print(f"  {i}. {pattern.description} (frequency: {pattern.frequency})")


async def code_generation_example():
    """Example: Generate code using AI."""
    print("\n🤖 Generating code...")
    
    # Initialize code generator
    generator = CodeGenerator()
    
    # Check available models
    models = generator.get_available_models()
    print(f"📦 Available models: {len(models)}")
    
    for model in models[:3]:  # Show first 3
        status = "✅ Loaded" if model["is_loaded"] else "⏳ Available"
        print(f"  • {model['name']}: {status}")
    
    # Create generation request
    request = GenerationRequest(
        prompt="def fibonacci(n):",
        language="python",
        max_tokens=200,
        temperature=0.1
    )
    
    try:
        # Generate code (this will fail without actual models)
        print("\n💡 Generating Fibonacci function...")
        response = generator.generate_code(request)
        
        print("✨ Generated code:")
        print(response.generated_code)
        print(f"\n⏱️  Generation time: {response.generation_time_ms:.1f}ms")
        print(f"🎯 Tokens generated: {response.tokens_generated}")
        
    except Exception as e:
        print(f"❌ Code generation failed: {e}")
        print("💡 This is expected without downloaded models")


async def configuration_example():
    """Example: Working with configuration."""
    print("\n⚙️  Configuration example...")
    
    # Load default configuration
    config = Config()
    
    print(f"📂 Data directory: {config.data_path}")
    print(f"🔧 Debug mode: {config.debug}")
    print(f"📝 Log level: {config.log_level}")
    
    # Show model configuration
    print(f"\n🤖 Model settings:")
    print(f"  • Default model: {config.model.name}")
    print(f"  • Max length: {config.model.max_length}")
    print(f"  • Temperature: {config.model.temperature}")
    print(f"  • Device: {config.model.device}")
    
    # Show security settings
    print(f"\n🔒 Security settings:")
    print(f"  • Encryption enabled: {config.security.enable_encryption}")
    print(f"  • Audit logging: {config.security.audit_logging}")
    
    # Show spider web settings
    print(f"\n🕸️  Spider web settings:")
    print(f"  • Federated learning: {config.spider.enable_federated_learning}")
    print(f"  • Privacy level: {config.spider.privacy_level}")


async def pattern_analysis_example():
    """Example: Detailed pattern analysis."""
    print("\n🎯 Pattern analysis example...")
    
    # Create sample Python code
    sample_code = '''
import os
import sys
from typing import List, Dict, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class User:
    """Represents a user in the system."""
    name: str
    email: str
    age: Optional[int] = None
    
    def __str__(self) -> str:
        return f"User({self.name}, {self.email})"
    
    def is_adult(self) -> bool:
        """Check if user is an adult."""
        return self.age is not None and self.age >= 18

def process_users(users: List[User]) -> Dict[str, User]:
    """Process a list of users and return a mapping."""
    result = {}
    
    for user in users:
        if user.email not in result:
            result[user.email] = user
        else:
            print(f"Duplicate user: {user.email}")
    
    return result

def main() -> None:
    """Main function."""
    users = [
        User("Alice", "<EMAIL>", 25),
        User("Bob", "<EMAIL>", 17),
        User("Charlie", "<EMAIL>"),
    ]
    
    processed = process_users(users)
    
    for email, user in processed.items():
        status = "adult" if user.is_adult() else "minor"
        print(f"{user.name} ({email}): {status}")

if __name__ == "__main__":
    main()
'''
    
    # Parse the sample code
    from codemind.analysis.parser import ParsedFile, SupportedLanguage
    
    parsed_file = ParsedFile(
        path=Path("sample.py"),
        language=SupportedLanguage.PYTHON,
        content=sample_code
    )
    
    # Extract patterns
    extractor = PatternExtractor()
    patterns = extractor.extract_from_file(parsed_file)
    
    print(f"🔍 Found {len(patterns)} patterns in sample code:")
    
    for pattern in patterns:
        print(f"\n📋 {pattern.type.value}:")
        print(f"  • Pattern: {pattern.pattern}")
        print(f"  • Description: {pattern.description}")
        print(f"  • Confidence: {pattern.confidence:.2f}")
        if pattern.examples:
            print(f"  • Example: {pattern.examples[0][:50]}...")


async def main():
    """Run all examples."""
    print("🧠 CodeMind Usage Examples")
    print("=" * 50)
    
    try:
        await analyze_codebase_example()
        await code_generation_example()
        await configuration_example()
        await pattern_analysis_example()
        
        print("\n✅ All examples completed!")
        print("\n🚀 Next steps:")
        print("  • Run 'codemind init' to initialize in your project")
        print("  • Run 'codemind analyze' to analyze your codebase")
        print("  • Run 'codemind suggest \"your feature\"' for AI suggestions")
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
