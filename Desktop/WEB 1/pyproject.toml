[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "codemind"
version = "0.1.0"
description = "The Self-Evolving Code Assistant - An autonomous CLI tool that learns from codebases"
readme = "README.md"
license = {text = "AGPL-3.0"}
authors = [
    {name = "CodeMind Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "CodeMind Team", email = "<EMAIL>"}
]
keywords = ["ai", "code-assistant", "machine-learning", "cli", "autonomous"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: GNU Affero General Public License v3",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
requires-python = ">=3.10"
dependencies = [
    # CLI Framework
    "typer[all]>=0.9.0",
    "rich>=13.0.0",
    "click>=8.0.0",
    
    # ML & AI
    "torch>=2.0.0",
    "transformers>=4.30.0",
    "accelerate>=0.20.0",
    "bitsandbytes>=0.39.0",
    "peft>=0.4.0",  # LoRA adapters
    
    # Code Analysis
    "tree-sitter>=0.20.0",
    "tree-sitter-python>=0.20.0",
    "tree-sitter-javascript>=0.20.0",
    "tree-sitter-typescript>=0.20.0",
    "tree-sitter-rust>=0.20.0",
    "tree-sitter-go>=0.20.0",
    "semgrep>=1.30.0",
    
    # Knowledge Storage
    "chromadb>=0.4.0",
    "sqlite-utils>=3.30.0",
    "sqlalchemy>=2.0.0",
    
    # Security & Crypto
    "pynacl>=1.5.0",
    "cryptography>=41.0.0",
    "keyring>=24.0.0",
    
    # Git & VCS
    "gitpython>=3.1.0",
    "dulwich>=0.21.0",
    
    # Utilities
    "pydantic>=2.0.0",
    "httpx>=0.24.0",
    "aiofiles>=23.0.0",
    "toml>=0.10.0",
    "pyyaml>=6.0.0",
    "python-dotenv>=1.0.0",
    "psutil>=5.9.0",
    
    # Monitoring & Logging
    "structlog>=23.0.0",
    "prometheus-client>=0.17.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.4.0",
    "pre-commit>=3.3.0",
]
gpu = [
    "torch[cuda]>=2.0.0",
    "nvidia-ml-py>=12.0.0",
]
enterprise = [
    "redis>=4.5.0",
    "celery>=5.3.0",
    "kubernetes>=27.0.0",
]

[project.urls]
Homepage = "https://github.com/codemind-ai/codemind"
Documentation = "https://docs.codemind.dev"
Repository = "https://github.com/codemind-ai/codemind"
"Bug Tracker" = "https://github.com/codemind-ai/codemind/issues"
Changelog = "https://github.com/codemind-ai/codemind/blob/main/CHANGELOG.md"

[project.scripts]
codemind = "codemind.cli:app"
cm = "codemind.cli:app"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["codemind"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=codemind",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gpu: marks tests that require GPU",
]

[tool.coverage.run]
source = ["src/codemind"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
